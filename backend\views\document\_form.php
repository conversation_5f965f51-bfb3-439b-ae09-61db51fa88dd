<?php

use yii\helpers\Html;
use kartik\form\ActiveForm;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use common\models\DocumentType;
use common\models\Gedetineerde;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\Rapport */
/* @var $form yii\widgets\ActiveForm */


// Prepare data for dropdown
$documents = ArrayHelper::map(DocumentType::find()->all(), 'id', 'type');

// Prepare gedetineerden data with multiple fields
$gedetineerden = ArrayHelper::map(
    Gedetineerde::find()->all(),
    'id',
    function ($model) {
        // return $model->regnr . ' - ' . $model->naam . ' ' . $model->voornaam . ' - ' . 'IDNR: ' . $model->idnr;
        return 'IDNR ' . $model->idnr . ' - ' . $model->naam . ' ' . $model->voornaam;
    }
);
?>

<div class="rapport-form">
    <?php $form = ActiveForm::begin([
        'id' => 'rapport-form',
        'options' => [
            'data-pjax' => true, // Add this to identify it as a Pjax form
        ],
    ]); ?>

    <div class="row">
        <div class="col-md-6">
            <?= $form->field($model, 'documenttype_id')->widget(Select2::classname(), [
                'data' => $documents,
                'options' => [
                    'placeholder' => 'Select a document...',
                    'id' => 'document-select'
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'dropdownParent' => '#ajaxCrudModal'
                ]
            ]); ?>
        </div>
        <div class="col-md-6">
            <?= $form->field($model, 'filename')->textInput(['maxlength' => true, 'id' => 'document-filename']) ?>
        </div>
    </div>

    <!-- Gedetineerde select -->
    <div id="gedetineerde-div" style="display:none;">
        <?= $form->field($model, 'gedetineerde_id')->widget(Select2::classname(), [
            'data' => $gedetineerden,
            'options' => [
                'placeholder' => 'Selecteer een gedetineerde...',
                'class' => 'form-control',
                'id' => 'gedetineerde-select'
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'minimumInputLength' => 1,
                'width' => '100%',
                'templateResult' => new \yii\web\JsExpression('function(data) {
                if (data.loading) return data.text;
                return data.text;
                }'),
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'dropdownParent' => '#ajaxCrudModal'
            ]
        ]); ?>
    </div>

    <div id="questions-container" style="display:none;">
        <div id="questions-list" class="mb-3">
            <!-- Questions will be loaded here -->
        </div>
    </div>

    <?php if (!Yii::$app->request->isAjax) { ?>
        <div class="form-group">
            <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
        </div>
    <?php } ?>

    <?php ActiveForm::end(); ?>

</div>

<?php
$getVragenUrl = Url::to(['document/get-vragen']);
$getGedetineerdeUrl = Url::to(['document/get-gedetineerde']);

$script = <<<JS
function updateFilename() {
    const gedetineerdeId = $('#gedetineerde-select').val();
    const documentId = $('#document-select').val();
    
    if (gedetineerdeId && documentId) {
        // Get gedetineerde data
        $.get('$getGedetineerdeUrl', { id: gedetineerdeId })
        .done(function(response) {
            if (response.success) {
                const gedetineerde = response.gedetineerde;
                const documentType = $('#document-select option:selected').text();
                const today = new Date();
                const dateStr = today.getDate().toString().padStart(2, '0') + '-' + 
                (today.getMonth() + 1).toString().padStart(2, '0') + '-' + 
                today.getFullYear();
                
                const filename = gedetineerde.voornaam + ' ' + gedetineerde.naam + ' - ' + documentType + ' ' + dateStr;
                $('#document-filename').val(filename);
                }
            });
    }
}

// When gedetineerde changes
$('#gedetineerde-select').on('change', function() {
    updateFilename();
});

// Function to handle document type selection
function handleDocumentTypeChange(docId) {
    const questionsContainer = $('#questions-container');
    const questionsList = $('#questions-list');
    const gedetineerdeField = $('#gedetineerde-div');

    if (!docId) {
        questionsContainer.hide();
        gedetineerdeField.hide();
        questionsList.empty();
        return;
    }

    // Fetch questions dynamically
    $.get('$getVragenUrl', { docId: docId })
        .done(function(response) {
            if (response.success) {
                questionsList.empty();

                // 🔑 Show gedetineerde only if handmatig_invoer is false (0)
                // If handmatig_invoer is true (1), hide the gedetineerde select
                if (response.document && response.document.handmatig_invoer == 1) {
                    gedetineerdeField.hide();
                } else {
                    gedetineerdeField.show();
                }

                // Render questions
                const questions = response.vragen;
                questions.forEach(function(item) {
                    const vraag = item.vraag;
                    questionsList.append(`
                    <div class="form-group mb-3">
                    <label class="control-label" for="answer-\${vraag.id}">
                    \${vraag.vraag_nummer}. \${vraag.vraag}
                    </label>
                    <input type="text"
                    id="answer-\${vraag.id}"
                    class="form-control"
                    name="Answers[\${vraag.id}]"
                    data-question-id="\${vraag.id}">
                    </div>
                    `);
                });

                questionsContainer.show();
            } else {
                questionsContainer.hide();
                gedetineerdeField.hide(); // Hide gedetineerde field if no document found
                questionsList.html('<div class="alert alert-warning">No questions found</div>');
            }
        })
        .fail(function() {
            questionsContainer.hide();
            gedetineerdeField.hide(); // Hide gedetineerde field on error
            questionsList.html('<div class="alert alert-danger">Error loading questions</div>');
        });
}

// When document type changes
$('#document-select').on('change', function() {
    updateFilename();
    handleDocumentTypeChange($(this).val());
});


// Prevent duplicate form submissions - use window property to avoid redeclaration
if (typeof window.isSubmitting === 'undefined') {
    window.isSubmitting = false;
}

$('#rapport-form').on('beforeSubmit', function(e) {
    if (window.isSubmitting) {
        return false;
    }
    
    window.isSubmitting = true;
    return true;
});
JS;

$this->registerJs($script);
?>