<?php

use common\models\DocumentType;
use common\models\Gedetineerde;
use yii\helpers\Html;
use kartik\form\ActiveForm;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model common\models\Rapport */
/* @var $form yii\widgets\ActiveForm */

$isAjax = false;

if (Yii::$app->request->isAjax) {
    $isAjax = true;
}

// Get document_id from URL and fetch questions
$documentId = Yii::$app->request->get('document_id');

$questions = [];
if ($documentId) {
    $questions = \common\models\DocumenttypeVraag::find()
        ->where(['document_id' => $documentId])
        ->orderBy(['vraag_nummer' => SORT_ASC])
        ->with(['vraag'])
        ->all();

    // Set the document_id in the model
    $model->documenttype_id = $documentId;
}

// Prepare data for dropdown
$documents = ArrayHelper::map(DocumentType::find()->all(), 'id', 'type');

// Prepare gedetineerden data with multiple fields
$gedetineerden = ArrayHelper::map(
    Gedetineerde::find()->all(),
    'id',
    function ($model) {
        // return $model->regnr . ' - ' . $model->naam . ' ' . $model->voornaam . ' - ' . 'IDNR: ' . $model->idnr;
        return 'IDNR ' . $model->idnr . ' - ' . $model->naam . ' ' . $model->voornaam;
    }
);

?>

<div class="rapport-form rounded-2">
    <?php if (!$isAjax) { ?>
        <div class="">
            <h3 class="mb-3"><?= $model->documentType->type ?></h3>
            <?php $form = ActiveForm::begin([
                'id' => 'rapport-form',
            ]); ?>

            <?= Html::activeHiddenInput($model, 'documenttype_id') ?>

            <!-- If handmatig_invoer is false, show the dropdown -->
            <?php if (!$model->documentType->handmatig_invoer): ?>
                <div class="row">
                    <div class="col-md-6">
                        <?= $form->field($model, 'gedetineerde_id')->widget(Select2::classname(), [
                            'data' => $gedetineerden,
                            'options' => [
                                'placeholder' => 'Selecteer een gedetineerde...',
                                'class' => 'form-control',
                                'id' => 'gedetineerde-select'
                            ],
                            'pluginOptions' => [
                                'allowClear' => true,
                                'minimumInputLength' => 1,
                                'width' => '100%',
                                'templateResult' => new \yii\web\JsExpression('function(data) {
                if (data.loading) return data.text;
                return data.text;
            }'),
                            ],
                            'pluginOptions' => [
                                'allowClear' => true,
                            ]
                        ]); ?>
                    </div>
                <?php endif; ?>

                <div class="col-md-6">
                    <?= $form->field($model, 'filename')->textInput(['maxlength' => true, 'id' => 'document-filename']) ?>
                </div>
                </div>
                <?php if (!empty($questions)): ?>
                    <div id="questions-container">
                        <?php foreach ($questions as $docVraag): ?>
                            <div class="form-group mb-3">
                                <label class="control-label required">
                                    <?= $docVraag->vraag_nummer ?>. <?= Html::encode($docVraag->vraag->vraag) ?>
                                </label>
                                <?= Html::textInput(
                                    "Answers[{$docVraag->vraag_id}]",
                                    '',
                                    [
                                        'class' => 'form-control',
                                        // 'required' => true,
                                        'data-question-id' => $docVraag->vraag_id
                                    ]
                                ) ?>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="form-group">
                        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        No questions found for this document.
                    </div>
                <?php endif; ?>

                <?php ActiveForm::end(); ?>
        </div>
    <?php } else { ?>
        <div>
            <?php $form = ActiveForm::begin([
                'id' => 'rapport-form',
                'options' => [
                    'data-pjax' => true, // Add this to identify it as a Pjax form
                ],
            ]); ?>

            <div class="row">
                <!-- Document type select -->
                <div class="col-md-6">
                    <?= $form->field($model, 'documenttype_id')->widget(Select2::classname(), [
                        'data' => $documents,
                        'options' => [
                            'placeholder' => 'Select a document...',
                            'id' => 'document-select'
                        ],
                        'pluginOptions' => [
                            'allowClear' => true,
                            'dropdownParent' => '#ajaxCrudModal'
                        ]
                    ]); ?>
                </div>
                <!-- Filename input -->
                <div class="col-md-6">
                    <?= $form->field($model, 'filename')->textInput(['maxlength' => true, 'id' => 'document-filename']) ?>
                </div>
            </div>


            <!-- Gedetineerde select -->
            <div id="gedetineerde-div" style="display:none;">
                <?= $form->field($model, 'gedetineerde_id')->widget(Select2::classname(), [
                    'data' => $gedetineerden,
                    'options' => [
                        'placeholder' => 'Selecteer een gedetineerde...',
                        'class' => 'form-control',
                        'id' => 'gedetineerde-select'
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'minimumInputLength' => 1,
                        'width' => '100%',
                        'templateResult' => new \yii\web\JsExpression('function(data) {
                                if (data.loading) return data.text;
                                return data.text;
                                }'),
                    ],
                    'pluginOptions' => [
                        'allowClear' => true,
                        'dropdownParent' => '#ajaxCrudModal'
                    ]
                ]); ?>
            </div>

            <div id="questions-container" style="display:none;">
                <div id="questions-list" class="mb-3">
                    <!-- Questions will be loaded here -->
                </div>
            </div>

            <?php if (!Yii::$app->request->isAjax) { ?>
                <div class="form-group">
                    <?= Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
                </div>
            <?php } ?>

            <?php ActiveForm::end(); ?>

        </div>
    <?php } ?>
</div>

<?php
$getVragenUrl = Url::to(['document/get-vragen']);
$getGedetineerdeUrl = Url::to(['document/get-gedetineerde']);

// Only set documentType if not Ajax and the relation exists
$documentType = null;
if (!$isAjax && $model->documentType) {
    $documentType = json_encode($model->documentType->type);
}

// Choose which JS block to register
if (!$isAjax) {
    // ---------------- NON-AJAX FORM ----------------
    $script = <<<JS
// Function to update filename (non-ajax form)
function updateFilename() {
    const gedetineerdeId = $('#gedetineerde-select').val();
    
    if (gedetineerdeId) {
        $.get('$getGedetineerdeUrl', { id: gedetineerdeId })
        .done(function(response) {
            if (response.success) {
                const gedetineerde = response.gedetineerde;
                const today = new Date();
                const dateStr = today.getDate().toString().padStart(2, '0') + '-' + 
                                (today.getMonth() + 1).toString().padStart(2, '0') + '-' + 
                                today.getFullYear();
                
                const filename = gedetineerde.voornaam + ' ' + gedetineerde.naam + ' - ' + $documentType + ' ' + dateStr;
                $('#document-filename').val(filename);
            }
        });
    }
}

// When gedetineerde changes
$('#gedetineerde-select').on('change', function() {
    updateFilename();
});

// Handle normal form submission with ajax
$('#rapport-form').on('beforeSubmit', function(e) {
    e.preventDefault();
    let form = $(this);

    $.ajax({
        url: form.attr('action'),
        type: 'POST',
        data: form.serialize(),
        success: function(response) {
            if (response.success) {
                Toast.fire({
                    icon: 'success',
                    title: "Document succesvol aangemaakt"
                });
                setTimeout(function() {
                    window.location.href = '/document';
                }, 500);
            } else {
                Toast.fire({
                    icon: 'error',
                    title: 'Error saving rapport ' + response.error
                });
            }
        },
        error: function(xhr, status, error) {
            alert('Error submitting form: ' + error);
        }
    });
    return false;
});
JS;
} else {
    // ---------------- AJAX FORM ----------------
    $script = <<<JS
// Function to update filename (ajax form)
function updateFilename() {
    const gedetineerdeId = $('#gedetineerde-select').val();
    const documentId = $('#document-select').val();
    
    if (gedetineerdeId && documentId) {
        $.get('$getGedetineerdeUrl', { id: gedetineerdeId })
        .done(function(response) {
            if (response.success) {
                const gedetineerde = response.gedetineerde;
                const documentType = $('#document-select option:selected').text();
                const today = new Date();
                const dateStr = today.getDate().toString().padStart(2, '0') + '-' + 
                                (today.getMonth() + 1).toString().padStart(2, '0') + '-' + 
                                today.getFullYear();
                
                const filename = gedetineerde.voornaam + ' ' + gedetineerde.naam + ' - ' + documentType + ' ' + dateStr;
                $('#document-filename').val(filename);
            }
        });
    }
}

// Handle ajax form submission
$('#rapport-form').on('beforeSubmit', function(e) {
    e.preventDefault();
    
    let form = $(this);
    let submitBtn = form.find(':submit');
    submitBtn.prop('disabled', true);
    
    $.ajax({
        url: form.attr('action'),
        type: 'POST',
        data: form.serialize(),
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                Toast.fire({
                    icon: 'success',
                    title: response.message
                });
                
                if (response.forceReload) {
                    $.pjax.reload({
                        container: response.forceReload
                    });
                }
                
                $('#ajaxCrudModal').modal('hide');
            } else {
                Toast.fire({
                    icon: 'error',
                    title: response.message || 'An error occurred'
                });
            }
        },
        error: function(xhr, status, error) {
            Toast.fire({
                icon: 'error',
                title: 'Error submitting form: ' + error
            });
        },
        complete: function() {
            submitBtn.prop('disabled', false);
        }
    });
    return false;
});

// When gedetineerde changes
$('#gedetineerde-select').on('change', function() {
    updateFilename();
});

// Function to handle document type selection
function handleDocumentTypeChange(docId) {
    const questionsContainer = $('#questions-container');
    const questionsList = $('#questions-list');
    const gedetineerdeField = $('#gedetineerde-div');

    if (!docId) {
        questionsContainer.hide();
        gedetineerdeField.hide();
        questionsList.empty();
        return;
    }

    // Fetch questions dynamically
    $.get('$getVragenUrl', { docId: docId })
        .done(function(response) {
            if (response.success) {
                questionsList.empty();

                // 🔑 Show gedetineerde only if handmatig_invoer is false (0)
                // If handmatig_invoer is true (1), hide the gedetineerde select
                if (response.document && response.document.handmatig_invoer == 1) {
                    gedetineerdeField.hide();
                } else {
                    gedetineerdeField.show();
                }

                // Render questions
                const questions = response.vragen;
                questions.forEach(function(item) {
                    const vraag = item.vraag;
                    questionsList.append(`
                    <div class="form-group mb-3">
                    <label class="control-label" for="answer-\${vraag.id}">
                    \${vraag.vraag_nummer}. \${vraag.vraag}
                    </label>
                    <input type="text"
                    id="answer-\${vraag.id}"
                    class="form-control"
                    name="Answers[\${vraag.id}]"
                    data-question-id="\${vraag.id}">
                    </div>
                    `);
                });

                questionsContainer.show();
            } else {
                questionsContainer.hide();
                gedetineerdeField.hide(); // Hide gedetineerde field if no document found
                questionsList.html('<div class="alert alert-warning">No questions found</div>');
            }
        })
        .fail(function() {
            questionsContainer.hide();
            gedetineerdeField.hide(); // Hide gedetineerde field on error
            questionsList.html('<div class="alert alert-danger">Error loading questions</div>');
        });
}

// When document type changes
$('#document-select').on('change', function() {
    updateFilename();
    handleDocumentTypeChange($(this).val());
});

// Check if there's already a selected document type on page load
$(document).ready(function() {
    const initialDocId = $('#document-select').val();
    if (initialDocId) {
        handleDocumentTypeChange(initialDocId);
    }
});
JS;
}

$this->registerJs($script);
?>