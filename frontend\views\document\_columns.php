<?php

use common\components\AccessHelper;
use common\components\DocumentButtonVisibilityHelper;
use yii\helpers\Html;
use yii\helpers\Url;

return [
    [
        'class' => 'kartik\grid\CheckboxColumn',
        'width' => '20px',
    ],
    [
        'class' => 'kartik\grid\SerialColumn',
        'width' => '30px',
    ],
    [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'filename',
    ],
    [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'documenttype_id',
        'format' => 'raw',
        'value' => function ($model) {
            $type = $model->documentType->type;
            return \common\widgets\Badge::widget([
                'label' => $type,
                'options' => ['class' => 'badge bg-info', 'title' => $type],
            ]);
        },
        'label' => 'Document Type'
    ],
    [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'status',
        'format' => 'raw',
        'hAlign' => 'center',
        'vAlign' => 'middle',
        // 'contentOptions' => ['style' => 'text-align:center;'],
        'value' => function ($model) {
            $statusLabel = ucfirst($model->workflowStatus->label);
            $firstLetter = mb_substr($statusLabel, 0, 1);
            $searchUrl = Url::to(['', 'DocumentSearch' => ['globalSearch' => $statusLabel]]);

            // Map status to Bootstrap badge classes
            $statusClassMap = [
                'medewerker' => 'badge bg-light',
                'onderhoofd' => 'badge bg-primary',
                'hoofd'      => 'badge bg-dark',
                'finale'      => 'badge bg-success',
            ];
            $statusKey = strtolower($model->workflowStatus->label);
            $badgeClass = $statusClassMap[$statusKey] ?? 'badge bg-info';

            return \common\widgets\Badge::widget([
                'label' => $firstLetter,
                'url' => $searchUrl,
                'options' => ['class' => $badgeClass, 'title' => $statusLabel],
            ]);
        },
    ],
    [
        'class' => '\kartik\grid\DataColumn',
        'attribute' => 'created_at',
        'format' => ['datetime'],
        'label' => 'Created At'
    ],
    // [
    //     'class' => '\kartik\grid\DataColumn',
    //     'attribute' => 'created_by',
    //     'value' => 'createdBy.username',
    //     'label' => 'Created By'
    // ],
    [
        'class' => 'kartik\grid\ActionColumn',
        'dropdown' => false,
        'noWrap' => 'true',
        'hAlign' => 'left',
        'template' => AccessHelper::buildActionTemplate(),
        'visibleButtons' => [
            'document' => fn($model, $key, $index) => DocumentButtonVisibilityHelper::showDocumentBtn($key),
            'signature' => fn($model, $key, $index) => DocumentButtonVisibilityHelper::showSignatureBtn($key),
            'update' => fn($model, $key, $index) => DocumentButtonVisibilityHelper::showUpdateBtn($key),
            'view' => fn($model, $key, $index) => DocumentButtonVisibilityHelper::showViewBtn($key),
        ],
        'vAlign' => 'middle',
        'urlCreator' => function ($action, $model, $key, $index) {
            switch ($action) {
                case 'comments':
                    return Url::to(['comments', 'id' => $key]);
                    break;
                case 'signature':
                    return Url::to(['signature', 'id' => $key]);
                    break;
                case 'document':
                    return Url::to(['view-document', 'id' => $key]);
                    break;
                default:
                    return Url::to([$action, 'id' => $key]);
                    break;
            }
        },
        'buttons' => [
            'comments' => function ($url, $model, $key) {
                return Html::a(
                    '<i data-lucide="message-circle" style="width:18px; height:18px"></i>',
                    $url,
                    [
                        'title' => Yii::t('yii2-ajaxcrud', 'Comments'),
                        'class' => 'btn btn-sm btn-outline-info',
                        'data-toggle' => 'tooltip',
                        'target' => '_blank', // This opens in a new tab
                    ]
                );
            },
            'signature' => function ($url, $model, $key) {
                return Html::a(
                    '<i data-lucide="signature" style="width:18px; height:18px"></i>',
                    $url,
                    [
                        'role' => 'modal-remote',
                        'title' => Yii::t('yii2-ajaxcrud', 'Signature'),
                        'class' => 'btn btn-sm btn-outline-warning',
                        'data-toggle' => 'tooltip',
                        // 'target' => '_blank', // This opens in a new tab
                    ]
                );
            },
            'document' => function ($url, $model, $key) {
                return Html::a(
                    '<i data-lucide="file-check" style="width:18px; height:18px"></i>',
                    $url,
                    [
                        'title' => Yii::t('yii2-ajaxcrud', 'Document'),
                        'class' => 'btn btn-sm btn-outline-dark',
                        'data-toggle' => 'tooltip',
                        'target' => '_blank', // This opens in a new tab
                        'rel' => 'noopener noreferrer', // Security best practice
                    ]
                );
            },
        ],
        'viewOptions' => ['role' => 'modal-remote', 'title' => Yii::t('yii2-ajaxcrud', 'View'), 'data-toggle' => 'tooltip', 'class' => 'btn btn-sm btn-outline-success'],
        'updateOptions' => ['role' => '', 'title' => Yii::t('yii2-ajaxcrud', 'Update'), 'data-toggle' => 'tooltip', 'class' => 'btn btn-sm btn-outline-primary'],
        'deleteOptions' => [
            'role' => 'modal-remote',
            'title' => Yii::t('yii2-ajaxcrud', 'Delete'),
            'class' => 'btn btn-sm btn-outline-danger',
            'data-confirm' => false,
            'data-method' => false, // for overide yii data api
            'data-request-method' => 'post',
            'data-toggle' => 'tooltip',
            'data-confirm-title' => Yii::t('yii2-ajaxcrud', 'Delete'),
            'data-confirm-message' => Yii::t('yii2-ajaxcrud', 'Delete Confirm')
        ],
    ],
];
