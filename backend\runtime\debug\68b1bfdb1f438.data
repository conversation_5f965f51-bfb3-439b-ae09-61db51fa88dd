a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:77754:"a:1:{s:8:"messages";a:123:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.068392;i:4;a:0:{}i:5;i:2912888;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.068988;i:4;a:0:{}i:5;i:3018112;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.068996;i:4;a:0:{}i:5;i:3018408;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.069268;i:4;a:0:{}i:5;i:3048408;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.069526;i:4;a:0:{}i:5;i:3075880;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.070362;i:4;a:0:{}i:5;i:3230912;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.07037;i:4;a:0:{}i:5;i:3231552;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.074503;i:4;a:0:{}i:5;i:4229168;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.082429;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5553264;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.082459;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5555544;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098155;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5613360;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.0993;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5634080;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.107996;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6103896;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.12682;i:4;a:0:{}i:5;i:6796416;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.12919;i:4;a:0:{}i:5;i:6976512;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.129226;i:4;a:0:{}i:5;i:6977152;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.131631;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047072;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.133803;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7058704;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.134688;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7058488;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.145284;i:4;a:0:{}i:5;i:7238920;}i:37;a:6:{i:0;s:44:"Route requested: 'role-functionality/routes'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.145394;i:4;a:0:{}i:5;i:7237872;}i:38;a:6:{i:0;s:39:"Route to run: role-functionality/routes";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.149565;i:4;a:0:{}i:5;i:7428000;}i:39;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-29 15:57:31')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.159392;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7699088;}i:42;a:6:{i:0;s:79:"Running action: backend\controllers\RoleFunctionalityController::actionRoutes()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.165271;i:4;a:0:{}i:5;i:7708480;}i:43;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringe4ca021fb1824286086a09f16f7ec667' AND (`expire` = 0 OR `expire` >**********)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.166219;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\RouteService.php";s:4:"line";i:28;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:269;s:8:"function";s:12:"getAllRoutes";s:5:"class";s:30:"common\components\RouteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:276;s:8:"function";s:16:"actionListRoutes";s:5:"class";s:47:"backend\controllers\RoleFunctionalityController";s:4:"type";s:2:"->";}}i:5;i:7851472;}i:46;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.167425;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7874776;}i:49;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.17401;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7885560;}i:52;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.175946;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7897296;}i:55;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.17691;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7897192;}i:58;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/debug/default/toolbar', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.184903;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8036944;}i:61;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.18876;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8043656;}i:64;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.189982;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8059224;}i:67;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.192228;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8065296;}i:70;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.193901;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8077696;}i:73;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.196928;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8084408;}i:76;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.197548;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8100456;}i:79;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.197886;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8105248;}i:82;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.198451;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8117648;}i:85;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.2009;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8124360;}i:88;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.201449;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8139768;}i:91;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.201809;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8144560;}i:94;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/events', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.202362;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8156960;}i:97;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.204397;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8163672;}i:100;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.205226;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8179080;}i:103;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.205731;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8183872;}i:106;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.206272;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8196272;}i:109;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.207973;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8202984;}i:112;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.208378;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8218392;}i:115;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.208738;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8223184;}i:118;a:6:{i:0;s:201:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/get-vragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.209506;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8236896;}i:121;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.211881;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8243608;}i:124;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.212536;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8259016;}i:127;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.21281;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8263808;}i:130;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/get-gedetineerde', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.213217;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8281872;}i:133;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.214999;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8288584;}i:136;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.215343;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8303992;}i:139;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.215561;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8308784;}i:142;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.215927;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8321184;}i:145;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.217795;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8327896;}i:148;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218421;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8343304;}i:151;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218637;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8348096;}i:154;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218976;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8360528;}i:157;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.220344;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8367240;}i:160;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.220651;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8382648;}i:163;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.220851;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8387440;}i:166;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.221181;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8399872;}i:169;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.222592;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8406584;}i:172;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.222885;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8421992;}i:175;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.223163;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8426784;}i:178;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.223865;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8439216;}i:181;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225673;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8445928;}i:184;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.226203;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8461336;}i:187;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.22656;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8466128;}i:190;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.227071;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8478528;}i:193;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.228683;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8490872;}i:196;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.229021;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8506280;}i:199;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.229256;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8511072;}i:202;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.23042;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8523472;}i:205;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.232221;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8530184;}i:208;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232513;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8545592;}i:211;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232697;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8550384;}i:214;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.233006;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8562784;}i:217;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.234364;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8569496;}i:220;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.234897;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8584904;}i:223;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.235326;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8589696;}i:226;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.23564;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8602096;}i:229;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.236819;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8608808;}i:232;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237109;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8624216;}i:235;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237303;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8629008;}i:238;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237603;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8641440;}i:241;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.238912;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8648152;}i:244;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239286;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8663560;}i:247;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239502;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8668352;}i:250;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.23982;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8680784;}i:253;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.241532;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8687496;}i:256;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.24206;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8702904;}i:259;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242275;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8711792;}i:262;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242944;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8724192;}i:265;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.244534;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8730904;}i:268;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.244994;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8746312;}i:271;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245292;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8751104;}i:274;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245646;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8763504;}i:277;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.246961;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8770216;}i:280;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.247482;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8785624;}i:283;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.247918;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8790416;}i:286;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.248258;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8802848;}i:289;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.249875;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8809560;}i:292;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250181;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8824968;}i:295;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250367;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8829760;}i:298;a:6:{i:0;s:194:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/pdf', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250683;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8842160;}i:301;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.251933;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8848872;}i:304;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252211;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8864280;}i:307;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252396;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8869072;}i:310;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252697;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8881504;}i:313;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.253794;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8888216;}i:316;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.254372;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8903624;}i:319;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.254785;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8908416;}i:322;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.255121;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8920816;}i:325;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.256413;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8927528;}i:328;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.256708;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8942936;}i:331;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.256893;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8947728;}i:334;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.25774;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8984832;}i:337;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.19827079772949, `memory_max`=9039440 WHERE `id`=3815";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.25813;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8988288;}}}";s:9:"profiling";s:150898:"a:3:{s:6:"memory";i:9661432;s:4:"time";d:0.20208287239074707;s:8:"messages";a:216:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.082466;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5556352;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.096066;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5599656;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.096113;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5599440;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098106;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5612072;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098169;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5614272;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098815;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5616848;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.099315;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5635120;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.100903;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5637648;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.108067;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6104280;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.108861;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6106648;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.131696;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047984;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.133768;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7057416;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.133815;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7059616;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.134589;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7061528;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.134699;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7060168;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.136247;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7062024;}i:40;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-29 15:57:31')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.15943;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7700448;}i:41;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-29 15:57:31')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.16468;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7702240;}i:44;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringe4ca021fb1824286086a09f16f7ec667' AND (`expire` = 0 OR `expire` >**********)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.166237;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\RouteService.php";s:4:"line";i:28;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:269;s:8:"function";s:12:"getAllRoutes";s:5:"class";s:30:"common\components\RouteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:276;s:8:"function";s:16:"actionListRoutes";s:5:"class";s:47:"backend\controllers\RoleFunctionalityController";s:4:"type";s:2:"->";}}i:5;i:7853352;}i:45;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringe4ca021fb1824286086a09f16f7ec667' AND (`expire` = 0 OR `expire` >**********)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.16704;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\RouteService.php";s:4:"line";i:28;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:269;s:8:"function";s:12:"getAllRoutes";s:5:"class";s:30:"common\components\RouteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:276;s:8:"function";s:16:"actionListRoutes";s:5:"class";s:47:"backend\controllers\RoleFunctionalityController";s:4:"type";s:2:"->";}}i:5;i:7863288;}i:47;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.167463;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7875904;}i:48;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.173901;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7876816;}i:50;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.174026;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7886472;}i:51;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.175812;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7896000;}i:53;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.175977;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7898208;}i:54;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.176608;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7900512;}i:56;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.176948;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7898232;}i:57;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.178648;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7901320;}i:59;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/debug/default/toolbar', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.184957;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8037656;}i:60;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/debug/default/toolbar', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.188612;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8038728;}i:62;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.188775;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8045536;}i:63;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.189505;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8049592;}i:65;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.190024;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8062384;}i:66;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.192179;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8064064;}i:68;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.192236;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8067176;}i:69;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.193021;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8068856;}i:71;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.193917;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8078408;}i:72;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.19679;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8079480;}i:74;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.196942;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8086288;}i:75;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.19743;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8090344;}i:77;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.197559;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8102336;}i:78;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.197852;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8104016;}i:80;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.197893;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8107128;}i:81;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.198182;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8108808;}i:83;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.198461;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8118360;}i:84;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.200766;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8119432;}i:86;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.200914;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8126240;}i:87;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.201351;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8130296;}i:89;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.201458;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8141648;}i:90;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.201778;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8143328;}i:92;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.201815;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8146440;}i:93;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.202112;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8148120;}i:95;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/events', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.202372;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8157672;}i:96;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/events', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.204301;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8158744;}i:98;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.204409;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8165552;}i:99;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.204835;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8169608;}i:101;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.205263;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8180960;}i:102;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.205696;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8182640;}i:104;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.205738;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8185752;}i:105;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.206032;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8187432;}i:107;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.206281;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8196984;}i:108;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.207897;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8198056;}i:110;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.207983;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8204864;}i:111;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.208283;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8208920;}i:113;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.208388;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8220272;}i:114;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.2087;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8221952;}i:116;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.208745;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8225064;}i:117;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.209205;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8226744;}i:119;a:6:{i:0;s:201:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/get-vragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.209518;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8237608;}i:120;a:6:{i:0;s:201:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/get-vragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.211538;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8238680;}i:122;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.211922;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8245488;}i:123;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.212423;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8249544;}i:125;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.212546;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8260896;}i:126;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.21278;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8262576;}i:128;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.212816;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8265688;}i:129;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.213001;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8273000;}i:131;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/get-gedetineerde', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.213225;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8282584;}i:132;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/get-gedetineerde', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.214926;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8283656;}i:134;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.215008;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8290464;}i:135;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.215254;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8294520;}i:137;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.21535;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8305872;}i:138;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.215534;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8307552;}i:140;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.215567;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8310664;}i:141;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.215729;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8312344;}i:143;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.215936;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8321896;}i:144;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.217468;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8322968;}i:146;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.217841;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8329776;}i:147;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.218338;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8333832;}i:149;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218428;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8345184;}i:150;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218614;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8346864;}i:152;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218643;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8349976;}i:153;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218807;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8351656;}i:155;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218984;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8361240;}i:156;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.220283;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8362312;}i:158;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.220351;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8369120;}i:159;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.220577;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8373176;}i:161;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.220658;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8384528;}i:162;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.220828;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8386208;}i:164;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.220857;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8389320;}i:165;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.221009;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8391000;}i:167;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.221189;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8400584;}i:168;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.222536;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8401720;}i:170;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.222599;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8408464;}i:171;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.222823;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8412520;}i:173;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.222891;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8423872;}i:174;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.223065;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8425552;}i:176;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.223395;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8428664;}i:177;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.223691;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8430344;}i:179;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.223872;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8439928;}i:180;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.225572;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8441000;}i:182;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225683;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8447808;}i:183;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.22603;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8451864;}i:185;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.226214;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8463216;}i:186;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.226461;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8464896;}i:188;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.226576;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8468008;}i:189;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.226856;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8469688;}i:191;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.227082;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8479240;}i:192;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.228603;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8480312;}i:194;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.228692;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8492752;}i:195;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.228949;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8496808;}i:197;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.229028;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8508160;}i:198;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.229235;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8509840;}i:200;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.229261;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8512952;}i:201;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.229448;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8514632;}i:203;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.230462;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8524184;}i:204;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232163;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8525256;}i:206;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.232228;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8532064;}i:207;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.23245;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8536120;}i:209;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232519;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8547472;}i:210;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232678;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8549152;}i:212;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232702;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8552264;}i:213;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232845;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8553944;}i:215;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.233013;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8563496;}i:216;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.234305;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8564568;}i:218;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.234372;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8571376;}i:219;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.234618;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8575432;}i:221;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.234932;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8586784;}i:222;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.235301;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8588464;}i:224;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.235331;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8591576;}i:225;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.235482;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8593256;}i:227;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.235646;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8602808;}i:228;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.236766;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8603880;}i:230;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.236827;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8610688;}i:231;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.237045;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8614744;}i:233;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237115;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8626096;}i:234;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237282;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8627776;}i:236;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237308;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8630888;}i:237;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237454;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8632568;}i:239;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237609;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8642152;}i:240;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.238823;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8643224;}i:242;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.238922;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8650032;}i:243;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.239197;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8654088;}i:245;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239294;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8665440;}i:246;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239477;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8667120;}i:248;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239508;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8670232;}i:249;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239648;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8671912;}i:251;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239827;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8681496;}i:252;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.241342;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8682568;}i:254;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.241569;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8689376;}i:255;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.241989;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8693432;}i:257;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242068;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8708880;}i:258;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242248;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8710560;}i:260;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242281;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8713672;}i:261;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242606;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8715352;}i:263;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242954;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8724904;}i:264;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.244441;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8725976;}i:266;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.244544;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8732784;}i:267;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.244894;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8736840;}i:269;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245003;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8748192;}i:270;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245259;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8749872;}i:272;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245298;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8752984;}i:273;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245461;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8754664;}i:275;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245653;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8764216;}i:276;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.246896;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8765288;}i:278;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.246969;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8772096;}i:279;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.247227;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8776152;}i:281;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.247516;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8787504;}i:282;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.247893;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8789184;}i:284;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.247924;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8792296;}i:285;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.248088;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8793976;}i:287;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.248265;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8803560;}i:288;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.249809;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8804632;}i:290;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.249885;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8811440;}i:291;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250112;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8815496;}i:293;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250187;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8826848;}i:294;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250346;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8828528;}i:296;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250372;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8831640;}i:297;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250517;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8833320;}i:299;a:6:{i:0;s:194:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/pdf', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250689;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8842872;}i:300;a:6:{i:0;s:194:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/pdf', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.251884;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8843944;}i:302;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.251939;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8850752;}i:303;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.252151;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8854808;}i:305;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252217;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8866160;}i:306;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252377;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8867840;}i:308;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252401;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8870952;}i:309;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252546;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8872632;}i:311;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252703;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8882216;}i:312;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.253742;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8883288;}i:314;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.253801;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8890096;}i:315;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.254042;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8894152;}i:317;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.254405;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8905504;}i:318;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.254761;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8907184;}i:320;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.25479;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8910296;}i:321;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.254951;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8911976;}i:323;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.255128;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8921528;}i:324;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.256361;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8922600;}i:326;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.256419;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8929408;}i:327;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.256645;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8933464;}i:329;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.256715;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8944816;}i:330;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.256873;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8946496;}i:332;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.256898;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8949608;}i:333;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.257039;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8951288;}i:335;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.25775;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8985960;}i:336;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.257986;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8987112;}i:338;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.19827079772949, `memory_max`=9039440 WHERE `id`=3815";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.258144;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8989632;}i:339;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.19827079772949, `memory_max`=9039440 WHERE `id`=3815";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.259845;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8991032;}}}";s:2:"db";s:150128:"a:1:{s:8:"messages";a:214:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.096113;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5599440;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098106;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5612072;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098169;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5614272;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098815;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5616848;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.099315;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5635120;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.100903;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5637648;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.108067;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6104280;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.108861;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6106648;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.131696;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047984;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.133768;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7057416;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.133815;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7059616;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.134589;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7061528;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.134699;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7060168;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.136247;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7062024;}i:40;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-29 15:57:31')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.15943;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7700448;}i:41;a:6:{i:0;s:172:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('role-functionality/routes', '1', '::1', 0, 'POST', '2025-08-29 15:57:31')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.16468;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7702240;}i:44;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringe4ca021fb1824286086a09f16f7ec667' AND (`expire` = 0 OR `expire` >**********)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.166237;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\RouteService.php";s:4:"line";i:28;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:269;s:8:"function";s:12:"getAllRoutes";s:5:"class";s:30:"common\components\RouteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:276;s:8:"function";s:16:"actionListRoutes";s:5:"class";s:47:"backend\controllers\RoleFunctionalityController";s:4:"type";s:2:"->";}}i:5;i:7853352;}i:45;a:6:{i:0;s:129:"SELECT `data` FROM `cache` WHERE `id` = 'reclasseringe4ca021fb1824286086a09f16f7ec667' AND (`expire` = 0 OR `expire` >**********)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.16704;i:4;a:3:{i:0;a:5:{s:4:"file";s:54:"C:\Web\Reclassering\common\components\RouteService.php";s:4:"line";i:28;s:8:"function";s:3:"get";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:269;s:8:"function";s:12:"getAllRoutes";s:5:"class";s:30:"common\components\RouteService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:276;s:8:"function";s:16:"actionListRoutes";s:5:"class";s:47:"backend\controllers\RoleFunctionalityController";s:4:"type";s:2:"->";}}i:5;i:7863288;}i:47;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.167463;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7875904;}i:48;a:6:{i:0;s:52:"DELETE FROM `role_functionality` WHERE `role_id`='4'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.173901;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:289;s:8:"function";s:9:"deleteAll";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"::";}}i:5;i:7876816;}i:50;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.174026;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7886472;}i:51;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.175812;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7896000;}i:53;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.175977;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7898208;}i:54;a:6:{i:0;s:38:"SHOW CREATE TABLE `role_functionality`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.176608;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7900512;}i:56;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.176948;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7898232;}i:57;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'role_functionality' AND `kcu`.`TABLE_NAME` = 'role_functionality'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.178648;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:294;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7901320;}i:59;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/debug/default/toolbar', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.184957;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8037656;}i:60;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/debug/default/toolbar', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.188612;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8038728;}i:62;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.188775;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8045536;}i:63;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.189505;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8049592;}i:65;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.190024;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8062384;}i:66;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.192179;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8064064;}i:68;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.192236;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8067176;}i:69;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.193021;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8068856;}i:71;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.193917;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8078408;}i:72;a:6:{i:0;s:192:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/site/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.19679;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8079480;}i:74;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.196942;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8086288;}i:75;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.19743;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8090344;}i:77;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.197559;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8102336;}i:78;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.197852;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8104016;}i:80;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.197893;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8107128;}i:81;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.198182;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8108808;}i:83;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.198461;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8118360;}i:84;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.200766;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8119432;}i:86;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.200914;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8126240;}i:87;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.201351;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8130296;}i:89;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.201458;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8141648;}i:90;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.201778;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8143328;}i:92;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.201815;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8146440;}i:93;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.202112;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8148120;}i:95;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/events', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.202372;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8157672;}i:96;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/events', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.204301;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8158744;}i:98;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.204409;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8165552;}i:99;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.204835;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8169608;}i:101;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.205263;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8180960;}i:102;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.205696;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8182640;}i:104;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.205738;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8185752;}i:105;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.206032;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8187432;}i:107;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.206281;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8196984;}i:108;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.207897;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8198056;}i:110;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.207983;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8204864;}i:111;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.208283;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8208920;}i:113;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.208388;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8220272;}i:114;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.2087;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8221952;}i:116;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.208745;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8225064;}i:117;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.209205;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8226744;}i:119;a:6:{i:0;s:201:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/get-vragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.209518;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8237608;}i:120;a:6:{i:0;s:201:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/get-vragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.211538;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8238680;}i:122;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.211922;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8245488;}i:123;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.212423;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8249544;}i:125;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.212546;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8260896;}i:126;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.21278;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8262576;}i:128;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.212816;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8265688;}i:129;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.213001;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8273000;}i:131;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/get-gedetineerde', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.213225;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8282584;}i:132;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/get-gedetineerde', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.214926;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8283656;}i:134;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.215008;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8290464;}i:135;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.215254;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8294520;}i:137;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.21535;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8305872;}i:138;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.215534;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8307552;}i:140;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.215567;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8310664;}i:141;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.215729;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8312344;}i:143;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.215936;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8321896;}i:144;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/afspraak/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.217468;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8322968;}i:146;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.217841;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8329776;}i:147;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.218338;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8333832;}i:149;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218428;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8345184;}i:150;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218614;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8346864;}i:152;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218643;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8349976;}i:153;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218807;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8351656;}i:155;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.218984;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8361240;}i:156;a:6:{i:0;s:207:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.220283;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8362312;}i:158;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.220351;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8369120;}i:159;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.220577;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8373176;}i:161;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.220658;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8384528;}i:162;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.220828;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8386208;}i:164;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.220857;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8389320;}i:165;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.221009;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8391000;}i:167;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.221189;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8400584;}i:168;a:6:{i:0;s:215:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-signature/save-signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.222536;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8401720;}i:170;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.222599;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8408464;}i:171;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.222823;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8412520;}i:173;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.222891;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8423872;}i:174;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.223065;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8425552;}i:176;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.223395;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8428664;}i:177;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.223691;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8430344;}i:179;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.223872;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8439928;}i:180;a:6:{i:0;s:204:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view-document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.225572;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8441000;}i:182;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.225683;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8447808;}i:183;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.22603;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8451864;}i:185;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.226214;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8463216;}i:186;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.226461;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8464896;}i:188;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.226576;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8468008;}i:189;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.226856;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8469688;}i:191;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.227082;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8479240;}i:192;a:6:{i:0;s:196:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.228603;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8480312;}i:194;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.228692;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8492752;}i:195;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.228949;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8496808;}i:197;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.229028;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8508160;}i:198;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.229235;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8509840;}i:200;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.229261;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8512952;}i:201;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.229448;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8514632;}i:203;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.230462;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8524184;}i:204;a:6:{i:0;s:195:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/view', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232163;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8525256;}i:206;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.232228;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8532064;}i:207;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.23245;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8536120;}i:209;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232519;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8547472;}i:210;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232678;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8549152;}i:212;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232702;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8552264;}i:213;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.232845;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8553944;}i:215;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.233013;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8563496;}i:216;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/update', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.234305;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8564568;}i:218;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.234372;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8571376;}i:219;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.234618;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8575432;}i:221;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.234932;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8586784;}i:222;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.235301;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8588464;}i:224;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.235331;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8591576;}i:225;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.235482;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8593256;}i:227;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.235646;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8602808;}i:228;a:6:{i:0;s:197:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/create', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.236766;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8603880;}i:230;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.236827;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8610688;}i:231;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.237045;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8614744;}i:233;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237115;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8626096;}i:234;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237282;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8627776;}i:236;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237308;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8630888;}i:237;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237454;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8632568;}i:239;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.237609;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8642152;}i:240;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/getvragen', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.238823;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8643224;}i:242;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.238922;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8650032;}i:243;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.239197;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8654088;}i:245;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239294;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8665440;}i:246;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239477;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8667120;}i:248;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239508;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8670232;}i:249;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239648;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8671912;}i:251;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.239827;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8681496;}i:252;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document-upload/index', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.241342;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8682568;}i:254;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.241569;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8689376;}i:255;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.241989;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8693432;}i:257;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242068;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8708880;}i:258;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242248;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8710560;}i:260;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242281;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8713672;}i:261;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242606;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8715352;}i:263;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.242954;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8724904;}i:264;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/dossiers', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.244441;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8725976;}i:266;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.244544;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8732784;}i:267;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.244894;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8736840;}i:269;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245003;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8748192;}i:270;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245259;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8749872;}i:272;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245298;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8752984;}i:273;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245461;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8754664;}i:275;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.245653;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8764216;}i:276;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/document', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.246896;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8765288;}i:278;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.246969;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8772096;}i:279;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.247227;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8776152;}i:281;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.247516;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8787504;}i:282;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.247893;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8789184;}i:284;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.247924;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8792296;}i:285;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.248088;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8793976;}i:287;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.248265;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8803560;}i:288;a:6:{i:0;s:200:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/signature', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.249809;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8804632;}i:290;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.249885;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8811440;}i:291;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.250112;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8815496;}i:293;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250187;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8826848;}i:294;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250346;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8828528;}i:296;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250372;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8831640;}i:297;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250517;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8833320;}i:299;a:6:{i:0;s:194:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/pdf', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.250689;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8842872;}i:300;a:6:{i:0;s:194:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/pdf', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.251884;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8843944;}i:302;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.251939;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8850752;}i:303;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.252151;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8854808;}i:305;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252217;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8866160;}i:306;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252377;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8867840;}i:308;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252401;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8870952;}i:309;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252546;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8872632;}i:311;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.252703;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8882216;}i:312;a:6:{i:0;s:203:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewdocument', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.253742;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8883288;}i:314;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.253801;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8890096;}i:315;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.254042;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8894152;}i:317;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.254405;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8905504;}i:318;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.254761;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8907184;}i:320;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.25479;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8910296;}i:321;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.254951;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8911976;}i:323;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.255128;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8921528;}i:324;a:6:{i:0;s:199:"INSERT INTO `role_functionality` (`role_id`, `route`, `created_by`, `updated_by`, `active`, `created_at`, `updated_at`) VALUES (4, '/document/viewfile', 1, 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.256361;i:4;a:1:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8922600;}i:326;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.256419;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8929408;}i:327;a:6:{i:0;s:40:"SELECT * FROM `user` WHERE `role_id`='4'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.256645;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:151;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8933464;}i:329;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.256715;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8944816;}i:330;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclasseringee3884871c292e38fe83a1ac97c0da3a'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.256873;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8946496;}i:332;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.256898;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8949608;}i:333;a:6:{i:0;s:77:"DELETE FROM `cache` WHERE `id`='reclassering52ccf0ffe17b4039dd9c45404547af44'";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.257039;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:153;s:8:"function";s:6:"delete";s:5:"class";s:17:"yii\caching\Cache";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\common\models\RoleFunctionality.php";s:4:"line";i:139;s:8:"function";s:19:"invalidateRoleCache";s:5:"class";s:31:"common\models\RoleFunctionality";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\backend\controllers\RoleFunctionalityController.php";s:4:"line";i:296;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8951288;}i:335;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.25775;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8985960;}i:336;a:6:{i:0;s:78:"SELECT * FROM `notification_trigger` WHERE `route`='role-functionality/routes'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.257986;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8987112;}i:338;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.19827079772949, `memory_max`=9039440 WHERE `id`=3815";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.258144;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8989632;}i:339;a:6:{i:0;s:90:"UPDATE `audit_entry` SET `duration`=0.19827079772949, `memory_max`=9039440 WHERE `id`=3815";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.259845;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:8991032;}}}";s:5:"event";s:45294:"a:252:{i:0;a:5:{s:4:"time";d:**********.080506;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:**********.096053;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:**********.109756;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:**********.109846;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:**********.121349;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:**********.145364;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:**********.15369;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:**********.153758;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:**********.157986;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:**********.165;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:**********.165013;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:**********.165214;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"backend\controllers\RoleFunctionalityController";}i:12;a:5:{s:4:"time";d:**********.165824;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:13;a:5:{s:4:"time";d:**********.173973;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:14;a:5:{s:4:"time";d:**********.178773;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:15;a:5:{s:4:"time";d:**********.183959;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:16;a:5:{s:4:"time";d:**********.184104;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:17;a:5:{s:4:"time";d:**********.188665;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:18;a:5:{s:4:"time";d:**********.188687;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:19;a:5:{s:4:"time";d:**********.189601;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:20;a:5:{s:4:"time";d:**********.189727;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:21;a:5:{s:4:"time";d:**********.189786;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:22;a:5:{s:4:"time";d:**********.189803;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:23;a:5:{s:4:"time";d:**********.193086;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:24;a:5:{s:4:"time";d:**********.193105;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:25;a:5:{s:4:"time";d:**********.19355;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:26;a:5:{s:4:"time";d:**********.19364;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:27;a:5:{s:4:"time";d:**********.19684;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:28;a:5:{s:4:"time";d:**********.19686;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:29;a:5:{s:4:"time";d:**********.197457;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:30;a:5:{s:4:"time";d:**********.197487;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:31;a:5:{s:4:"time";d:**********.1975;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:32;a:5:{s:4:"time";d:**********.197503;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:33;a:5:{s:4:"time";d:**********.198214;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:34;a:5:{s:4:"time";d:**********.198228;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:35;a:5:{s:4:"time";d:**********.198365;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:36;a:5:{s:4:"time";d:**********.198396;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:37;a:5:{s:4:"time";d:**********.200817;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:38;a:5:{s:4:"time";d:**********.200836;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:39;a:5:{s:4:"time";d:**********.201374;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:40;a:5:{s:4:"time";d:**********.201401;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:41;a:5:{s:4:"time";d:**********.201415;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:42;a:5:{s:4:"time";d:**********.201418;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:43;a:5:{s:4:"time";d:**********.202142;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:44;a:5:{s:4:"time";d:**********.202157;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:45;a:5:{s:4:"time";d:**********.202283;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:46;a:5:{s:4:"time";d:**********.20231;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:47;a:5:{s:4:"time";d:**********.204336;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:48;a:5:{s:4:"time";d:**********.20435;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:49;a:5:{s:4:"time";d:**********.204905;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:50;a:5:{s:4:"time";d:**********.205015;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:51;a:5:{s:4:"time";d:**********.205075;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:52;a:5:{s:4:"time";d:**********.205093;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:53;a:5:{s:4:"time";d:**********.206062;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:54;a:5:{s:4:"time";d:**********.206074;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:55;a:5:{s:4:"time";d:**********.206201;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:56;a:5:{s:4:"time";d:**********.206226;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:57;a:5:{s:4:"time";d:**********.207924;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:58;a:5:{s:4:"time";d:**********.207935;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:59;a:5:{s:4:"time";d:**********.208308;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:60;a:5:{s:4:"time";d:**********.208333;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:61;a:5:{s:4:"time";d:**********.208346;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:62;a:5:{s:4:"time";d:**********.208349;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:63;a:5:{s:4:"time";d:**********.209259;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:64;a:5:{s:4:"time";d:**********.209274;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:65;a:5:{s:4:"time";d:**********.209424;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:66;a:5:{s:4:"time";d:**********.209452;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:67;a:5:{s:4:"time";d:**********.211656;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:68;a:5:{s:4:"time";d:**********.211712;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:69;a:5:{s:4:"time";d:**********.212459;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:70;a:5:{s:4:"time";d:**********.212486;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:71;a:5:{s:4:"time";d:**********.212498;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:72;a:5:{s:4:"time";d:**********.212501;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:73;a:5:{s:4:"time";d:**********.213028;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:74;a:5:{s:4:"time";d:**********.213039;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:75;a:5:{s:4:"time";d:**********.213148;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:76;a:5:{s:4:"time";d:**********.213172;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:77;a:5:{s:4:"time";d:**********.214952;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:78;a:5:{s:4:"time";d:**********.214964;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:79;a:5:{s:4:"time";d:**********.215282;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:80;a:5:{s:4:"time";d:**********.215304;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:81;a:5:{s:4:"time";d:**********.215315;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:82;a:5:{s:4:"time";d:**********.215317;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:83;a:5:{s:4:"time";d:**********.215753;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:84;a:5:{s:4:"time";d:**********.215764;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:85;a:5:{s:4:"time";d:**********.215862;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:86;a:5:{s:4:"time";d:**********.215884;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:87;a:5:{s:4:"time";d:**********.217565;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:88;a:5:{s:4:"time";d:**********.217614;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:89;a:5:{s:4:"time";d:**********.21836;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:90;a:5:{s:4:"time";d:**********.21838;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:91;a:5:{s:4:"time";d:**********.218391;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:92;a:5:{s:4:"time";d:**********.218393;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:93;a:5:{s:4:"time";d:**********.218828;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:94;a:5:{s:4:"time";d:**********.218837;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:95;a:5:{s:4:"time";d:**********.218922;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:96;a:5:{s:4:"time";d:**********.218941;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:97;a:5:{s:4:"time";d:**********.220302;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:98;a:5:{s:4:"time";d:**********.220312;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:99;a:5:{s:4:"time";d:**********.220592;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:100;a:5:{s:4:"time";d:**********.220615;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:101;a:5:{s:4:"time";d:**********.220625;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:102;a:5:{s:4:"time";d:**********.220628;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:103;a:5:{s:4:"time";d:**********.22103;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:104;a:5:{s:4:"time";d:**********.221039;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:105;a:5:{s:4:"time";d:**********.22113;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:106;a:5:{s:4:"time";d:**********.221148;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:107;a:5:{s:4:"time";d:**********.222554;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:108;a:5:{s:4:"time";d:**********.222563;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:109;a:5:{s:4:"time";d:**********.222837;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:110;a:5:{s:4:"time";d:**********.222854;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:111;a:5:{s:4:"time";d:**********.222863;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:112;a:5:{s:4:"time";d:**********.222866;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:113;a:5:{s:4:"time";d:**********.22372;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:114;a:5:{s:4:"time";d:**********.223729;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:115;a:5:{s:4:"time";d:**********.22381;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:116;a:5:{s:4:"time";d:**********.223827;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:117;a:5:{s:4:"time";d:**********.225621;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:118;a:5:{s:4:"time";d:**********.225633;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:119;a:5:{s:4:"time";d:**********.226071;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:120;a:5:{s:4:"time";d:**********.226146;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:121;a:5:{s:4:"time";d:**********.226163;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:122;a:5:{s:4:"time";d:**********.226166;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:123;a:5:{s:4:"time";d:**********.226891;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:124;a:5:{s:4:"time";d:**********.226904;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:125;a:5:{s:4:"time";d:**********.227009;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:126;a:5:{s:4:"time";d:**********.22703;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:127;a:5:{s:4:"time";d:**********.228631;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:128;a:5:{s:4:"time";d:**********.228644;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:129;a:5:{s:4:"time";d:**********.228966;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:130;a:5:{s:4:"time";d:**********.228986;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:131;a:5:{s:4:"time";d:**********.228996;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:132;a:5:{s:4:"time";d:**********.228998;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:133;a:5:{s:4:"time";d:**********.229545;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:134;a:5:{s:4:"time";d:**********.229604;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:135;a:5:{s:4:"time";d:**********.230107;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:136;a:5:{s:4:"time";d:**********.230213;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:137;a:5:{s:4:"time";d:**********.232182;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:138;a:5:{s:4:"time";d:**********.232191;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:139;a:5:{s:4:"time";d:**********.232464;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:140;a:5:{s:4:"time";d:**********.232481;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:141;a:5:{s:4:"time";d:**********.23249;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:142;a:5:{s:4:"time";d:**********.232493;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:143;a:5:{s:4:"time";d:**********.232863;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:144;a:5:{s:4:"time";d:**********.232871;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:145;a:5:{s:4:"time";d:**********.232955;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:146;a:5:{s:4:"time";d:**********.232972;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:147;a:5:{s:4:"time";d:**********.234324;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:148;a:5:{s:4:"time";d:**********.234333;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:149;a:5:{s:4:"time";d:**********.234659;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:150;a:5:{s:4:"time";d:**********.234741;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:151;a:5:{s:4:"time";d:**********.234764;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:152;a:5:{s:4:"time";d:**********.234781;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:153;a:5:{s:4:"time";d:**********.235503;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:154;a:5:{s:4:"time";d:**********.235511;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:155;a:5:{s:4:"time";d:**********.235592;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:156;a:5:{s:4:"time";d:**********.235608;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:157;a:5:{s:4:"time";d:**********.236784;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:158;a:5:{s:4:"time";d:**********.236792;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:159;a:5:{s:4:"time";d:**********.237059;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:160;a:5:{s:4:"time";d:**********.237077;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:161;a:5:{s:4:"time";d:**********.237087;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:162;a:5:{s:4:"time";d:**********.237089;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:163;a:5:{s:4:"time";d:**********.237473;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:164;a:5:{s:4:"time";d:**********.23748;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:165;a:5:{s:4:"time";d:**********.237556;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:166;a:5:{s:4:"time";d:**********.237572;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:167;a:5:{s:4:"time";d:**********.238854;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:168;a:5:{s:4:"time";d:**********.238866;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:169;a:5:{s:4:"time";d:**********.239222;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:170;a:5:{s:4:"time";d:**********.239245;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:171;a:5:{s:4:"time";d:**********.239255;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:172;a:5:{s:4:"time";d:**********.239258;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:173;a:5:{s:4:"time";d:**********.239671;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:174;a:5:{s:4:"time";d:**********.239681;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:175;a:5:{s:4:"time";d:**********.239766;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:176;a:5:{s:4:"time";d:**********.239784;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:177;a:5:{s:4:"time";d:**********.241384;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:178;a:5:{s:4:"time";d:**********.241401;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:179;a:5:{s:4:"time";d:**********.242008;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:180;a:5:{s:4:"time";d:**********.242026;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:181;a:5:{s:4:"time";d:**********.242035;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:182;a:5:{s:4:"time";d:**********.242038;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:183;a:5:{s:4:"time";d:**********.24271;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:184;a:5:{s:4:"time";d:**********.242736;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:185;a:5:{s:4:"time";d:**********.242871;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:186;a:5:{s:4:"time";d:**********.242895;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:187;a:5:{s:4:"time";d:**********.244472;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:188;a:5:{s:4:"time";d:**********.244487;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:189;a:5:{s:4:"time";d:**********.244923;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:190;a:5:{s:4:"time";d:**********.244948;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:191;a:5:{s:4:"time";d:**********.244959;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:192;a:5:{s:4:"time";d:**********.244961;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:193;a:5:{s:4:"time";d:**********.245487;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:194;a:5:{s:4:"time";d:**********.245498;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:195;a:5:{s:4:"time";d:**********.245589;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:196;a:5:{s:4:"time";d:**********.245609;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:197;a:5:{s:4:"time";d:**********.246917;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:198;a:5:{s:4:"time";d:**********.246928;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:199;a:5:{s:4:"time";d:**********.247264;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:200;a:5:{s:4:"time";d:**********.247301;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:201;a:5:{s:4:"time";d:**********.24735;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:202;a:5:{s:4:"time";d:**********.247367;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:203;a:5:{s:4:"time";d:**********.24811;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:204;a:5:{s:4:"time";d:**********.248118;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:205;a:5:{s:4:"time";d:**********.248203;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:206;a:5:{s:4:"time";d:**********.248224;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:207;a:5:{s:4:"time";d:**********.249832;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:208;a:5:{s:4:"time";d:**********.249843;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:209;a:5:{s:4:"time";d:**********.250128;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:210;a:5:{s:4:"time";d:**********.250146;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:211;a:5:{s:4:"time";d:**********.250156;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:212;a:5:{s:4:"time";d:**********.250158;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:213;a:5:{s:4:"time";d:**********.250536;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:214;a:5:{s:4:"time";d:**********.250545;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:215;a:5:{s:4:"time";d:**********.250629;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:216;a:5:{s:4:"time";d:**********.250647;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:217;a:5:{s:4:"time";d:**********.251899;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:218;a:5:{s:4:"time";d:**********.251907;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:219;a:5:{s:4:"time";d:**********.252164;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:220;a:5:{s:4:"time";d:**********.252181;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:221;a:5:{s:4:"time";d:**********.25219;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:222;a:5:{s:4:"time";d:**********.252192;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:223;a:5:{s:4:"time";d:**********.252565;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:224;a:5:{s:4:"time";d:**********.252573;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:225;a:5:{s:4:"time";d:**********.252649;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:226;a:5:{s:4:"time";d:**********.252665;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:227;a:5:{s:4:"time";d:**********.253758;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:228;a:5:{s:4:"time";d:**********.253766;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:229;a:5:{s:4:"time";d:**********.254097;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:230;a:5:{s:4:"time";d:**********.254188;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:231;a:5:{s:4:"time";d:**********.254243;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:232;a:5:{s:4:"time";d:**********.254259;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:233;a:5:{s:4:"time";d:**********.254972;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:234;a:5:{s:4:"time";d:**********.254981;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:235;a:5:{s:4:"time";d:**********.255068;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:236;a:5:{s:4:"time";d:**********.255087;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:237;a:5:{s:4:"time";d:**********.256377;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\RoleFunctionality";}i:238;a:5:{s:4:"time";d:**********.256386;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:239;a:5:{s:4:"time";d:**********.256658;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:240;a:5:{s:4:"time";d:**********.256676;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:241;a:5:{s:4:"time";d:**********.256686;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:242;a:5:{s:4:"time";d:**********.256688;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:243;a:5:{s:4:"time";d:**********.257526;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"backend\controllers\RoleFunctionalityController";}i:244;a:5:{s:4:"time";d:**********.257699;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:245;a:5:{s:4:"time";d:**********.258008;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:246;a:5:{s:4:"time";d:**********.258057;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:247;a:5:{s:4:"time";d:**********.259882;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:248;a:5:{s:4:"time";d:**********.25989;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:249;a:5:{s:4:"time";d:**********.259896;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:250;a:5:{s:4:"time";d:**********.260204;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:251;a:5:{s:4:"time";d:**********.260249;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.059755;s:3:"end";d:**********.264125;s:6:"memory";i:9661432;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:335:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.145377;i:4;a:0:{}i:5;i:7237952;}}s:5:"route";s:25:"role-functionality/routes";s:6:"action";s:63:"backend\controllers\RoleFunctionalityController::actionRoutes()";}";s:7:"request";s:15090:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:302;s:14:"requestHeaders";a:20:{s:12:"content-type";s:33:"application/x-www-form-urlencoded";s:14:"content-length";s:4:"1508";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:6:"origin";s:21:"http://localhost:8005";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:7:"referer";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:957:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=kg5944qo99nlrpbhm7q5konqli; _csrf-backend=917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22TwezWto5toyW6sW07LndJjdZNt6UE7DV%22%3B%7D; advanced-frontend-fmz=moh7rs0lensqa7tdlnifptpb87; _csrf-frontend=dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";s:13:"cache-control";s:9:"max-age=0";}s:15:"responseHeaders";a:10:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:8:"Location";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68b1bfdb1f438";s:16:"X-Debug-Duration";s:3:"201";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68b1bfdb1f438";s:10:"Set-Cookie";s:311:"_identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; expires=Sun, 28 Sep 2025 14:57:31 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:25:"role-functionality/routes";s:6:"action";s:63:"backend\controllers\RoleFunctionalityController::actionRoutes()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:33:"application/x-www-form-urlencoded";s:3:"Raw";s:1508:"_csrf-backend=E-OtpBD2OAFVHWoOs9HMWNpSLzCHq3fSONm0MBDCjlRHlMjeR4JXNCFyE1mFopto7R5BVM3BE4h2rYJlVfXKAg%3D%3D&RoleFunctionality%5Brole_id%5D=4&custom_route=&RoleFunctionality%5Broute%5D%5B%5D=%2Fdebug%2Fdefault%2Ftoolbar&RoleFunctionality%5Broute%5D%5B%5D=%2Fsite%2Findex&RoleFunctionality%5Broute%5D%5B%5D=%2Fafspraak%2Fcreate&RoleFunctionality%5Broute%5D%5B%5D=%2Fafspraak%2Fevents&RoleFunctionality%5Broute%5D%5B%5D=%2Fafspraak%2Fview&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fget-vragen&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fget-gedetineerde&RoleFunctionality%5Broute%5D%5B%5D=%2Fafspraak%2Findex&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument-signature%2Fcreate&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument-signature%2Fsave-signature&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fview-document&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Findex&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fview&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fupdate&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fcreate&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fgetvragen&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument-upload%2Findex&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fdossiers&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fdocument&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fsignature&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fpdf&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fviewdocument&RoleFunctionality%5Broute%5D%5B%5D=%2Fdocument%2Fviewfile";s:7:"Decoded";a:3:{s:13:"_csrf-backend";s:88:"E-OtpBD2OAFVHWoOs9HMWNpSLzCHq3fSONm0MBDCjlRHlMjeR4JXNCFyE1mFopto7R5BVM3BE4h2rYJlVfXKAg==";s:17:"RoleFunctionality";a:2:{s:7:"role_id";s:1:"4";s:5:"route";a:23:{i:0;s:22:"/debug/default/toolbar";i:1;s:11:"/site/index";i:2;s:16:"/afspraak/create";i:3;s:16:"/afspraak/events";i:4;s:14:"/afspraak/view";i:5;s:20:"/document/get-vragen";i:6;s:26:"/document/get-gedetineerde";i:7;s:15:"/afspraak/index";i:8;s:26:"/document-signature/create";i:9;s:34:"/document-signature/save-signature";i:10;s:23:"/document/view-document";i:11;s:15:"/document/index";i:12;s:14:"/document/view";i:13;s:16:"/document/update";i:14;s:16:"/document/create";i:15;s:19:"/document/getvragen";i:16;s:22:"/document-upload/index";i:17;s:18:"/document/dossiers";i:18;s:18:"/document/document";i:19;s:19:"/document/signature";i:20;s:13:"/document/pdf";i:21;s:22:"/document/viewdocument";i:22;s:18:"/document/viewfile";}}s:12:"custom_route";s:0:"";}}s:6:"SERVER";a:108:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-0e96b12e-26fd-4089-a913-8a68e8cb6f9f";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:61:"/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:14:"REQUEST_METHOD";s:4:"POST";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"49733";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:39:"r=role-functionality%2Froutes&role_id=4";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:14:"CONTENT_LENGTH";s:4:"1508";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:11:"HTTP_ORIGIN";s:21:"http://localhost:8005";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:12:"HTTP_REFERER";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:957:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=kg5944qo99nlrpbhm7q5konqli; _csrf-backend=917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22TwezWto5toyW6sW07LndJjdZNt6UE7DV%22%3B%7D; advanced-frontend-fmz=moh7rs0lensqa7tdlnifptpb87; _csrf-frontend=dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:17:"HTTP_CONTENT_TYPE";s:33:"application/x-www-form-urlencoded";s:19:"HTTP_CONTENT_LENGTH";s:4:"1508";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.053693;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:2:{s:1:"r";s:25:"role-functionality/routes";s:7:"role_id";s:1:"4";}s:4:"POST";a:3:{s:13:"_csrf-backend";s:88:"E-OtpBD2OAFVHWoOs9HMWNpSLzCHq3fSONm0MBDCjlRHlMjeR4JXNCFyE1mFopto7R5BVM3BE4h2rYJlVfXKAg==";s:17:"RoleFunctionality";a:2:{s:7:"role_id";s:1:"4";s:5:"route";a:23:{i:0;s:22:"/debug/default/toolbar";i:1;s:11:"/site/index";i:2;s:16:"/afspraak/create";i:3;s:16:"/afspraak/events";i:4;s:14:"/afspraak/view";i:5;s:20:"/document/get-vragen";i:6;s:26:"/document/get-gedetineerde";i:7;s:15:"/afspraak/index";i:8;s:26:"/document-signature/create";i:9;s:34:"/document-signature/save-signature";i:10;s:23:"/document/view-document";i:11;s:15:"/document/index";i:12;s:14:"/document/view";i:13;s:16:"/document/update";i:14;s:16:"/document/create";i:15;s:19:"/document/getvragen";i:16;s:22:"/document-upload/index";i:17;s:18:"/document/dossiers";i:18;s:18:"/document/document";i:19;s:19:"/document/signature";i:20;s:13:"/document/pdf";i:21;s:22:"/document/viewdocument";i:22;s:18:"/document/viewfile";}}s:12:"custom_route";s:0:"";}s:6:"COOKIE";a:7:{s:18:"_identity-frontend";s:158:"ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"_identity-backend";s:157:"39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:20:"advanced-backend-fmz";s:26:"kg5944qo99nlrpbhm7q5konqli";s:13:"_csrf-backend";s:139:"917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"TwezWto5toyW6sW07LndJjdZNt6UE7DV";}";s:21:"advanced-frontend-fmz";s:26:"moh7rs0lensqa7tdlnifptpb87";s:14:"_csrf-frontend";s:140:"dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:4:{s:7:"__flash";a:1:{s:7:"success";i:-1;}s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";s:7:"success";s:28:"Routes updated successfully.";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68b1bfdb1f438";s:3:"url";s:82:"http://localhost:8005/backoffice/index.php?r=role-functionality%2Froutes&role_id=4";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:3:"::1";s:4:"time";d:**********.053693;s:10:"statusCode";i:302;s:8:"sqlCount";i:107;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9661432;s:14:"processingTime";d:0.20208287239074707;}s:10:"exceptions";a:0:{}}