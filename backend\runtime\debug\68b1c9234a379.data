a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:50945:"a:1:{s:8:"messages";a:91:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756481827.244612;i:4;a:0:{}i:5;i:2898736;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756481827.245861;i:4;a:0:{}i:5;i:3003960;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1756481827.245892;i:4;a:0:{}i:5;i:3004256;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756481827.247651;i:4;a:0:{}i:5;i:3034256;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756481827.248693;i:4;a:0:{}i:5;i:3061728;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756481827.252776;i:4;a:0:{}i:5;i:3216760;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1756481827.252798;i:4;a:0:{}i:5;i:3217400;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1756481827.259574;i:4;a:0:{}i:5;i:4215016;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.275357;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5539064;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1756481827.275399;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5541344;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.286458;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5599160;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.28775;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5619880;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.293259;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6089696;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756481827.303608;i:4;a:0:{}i:5;i:6782200;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756481827.304511;i:4;a:0:{}i:5;i:6962296;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1756481827.30452;i:4;a:0:{}i:5;i:6962936;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.305339;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7032856;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.307081;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7044488;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.307877;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7044272;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756481827.317944;i:4;a:0:{}i:5;i:7224704;}i:37;a:6:{i:0;s:36:"Route requested: 'document/comments'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1756481827.318002;i:4;a:0:{}i:5;i:7223640;}i:38;a:6:{i:0;s:31:"Route to run: document/comments";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1756481827.319699;i:4;a:0:{}i:5;i:7439104;}i:39;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/comments', '1', '::1', 0, 'GET', '2025-08-29 16:37:07')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1756481827.322534;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7709488;}i:42;a:6:{i:0;s:72:"Running action: backend\controllers\DocumentController::actionComments()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1756481827.327149;i:4;a:0:{}i:5;i:7720936;}i:43;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.327464;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7768632;}i:46;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.330106;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7783432;}i:49;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.332564;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7787096;}i:52;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.336059;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7791944;}i:55;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.339387;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8447560;}i:58;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.340695;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8456904;}i:61;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.341316;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8461224;}i:64;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.34251;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8465096;}i:67;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.34398;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8538560;}i:70;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.345518;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8589280;}i:73;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.348558;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8598960;}i:76;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.350515;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8599624;}i:79;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.352009;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8604552;}i:82;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.353538;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8743656;}i:85;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.35411;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8758016;}i:88;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.355436;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8769712;}i:91;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.355964;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8771024;}i:94;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.357483;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8776464;}i:97;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.357893;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8787024;}i:100;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.358258;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8797720;}i:103;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.358819;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8815232;}i:106;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.360069;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8826944;}i:109;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.360632;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8828240;}i:112;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.364365;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8924584;}i:115;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.366845;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8939072;}i:118;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.369163;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8949624;}i:121;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.369842;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8951272;}i:124;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.371612;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9015464;}i:127;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.372222;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9020432;}i:130;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.373336;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9040232;}i:133;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.374015;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9057904;}i:136;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.375362;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9069488;}i:139;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.37601;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9070896;}i:142;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.377862;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9141008;}i:145;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.378884;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9217208;}i:148;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.379961;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9229032;}i:151;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.381191;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9240640;}i:154;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.382104;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9257880;}i:157;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.383397;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9269488;}i:160;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.384123;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9281096;}i:163;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.384713;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9292704;}i:166;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.385256;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9304312;}i:169;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.385793;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9315920;}i:172;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.386353;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9327688;}i:175;a:6:{i:0;s:76:"Rendering view file: C:\Web\Reclassering\backend\views\document\comments.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756481827.388067;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9527512;}i:176;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.407252;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:91;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10493176;}i:179;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.409058;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10575160;}i:182;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.410766;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10589008;}i:185;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.411738;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10589472;}i:188;a:6:{i:0;s:93:"Rendering view file: C:\Web\Reclassering\backend\views\document-signature/_signature-form.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756481827.414514;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:327;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10593696;}i:189;a:6:{i:0;s:71:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756481827.415681;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10578128;}i:190;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\navbar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756481827.420533;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10984376;}i:191;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.421182;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11070360;}i:194;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.422101;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11075296;}i:197;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.422699;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11085048;}i:200;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.42416;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11097064;}i:203;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.424806;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11099968;}i:206;a:6:{i:0;s:79:"Rendering view file: C:\Web\Reclassering\common\widgets\views\notifications.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756481827.426405;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:27;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11102552;}i:207;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\profile.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756481827.427148;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\ProfileWidget.php";s:4:"line";i:32;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:155;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11112096;}i:208;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756481827.427379;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11075656;}i:209;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756481827.427704;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11121688;}i:210;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.428547;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11199560;}i:213;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\content.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756481827.431894;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:52;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11251072;}i:214;a:6:{i:0;s:82:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\control-sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756481827.432628;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:56;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11290832;}i:215;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\footer.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756481827.432797;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11293152;}i:216;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/comments'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.433981;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11326968;}i:219;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.20237016677856, `memory_max`=11479440 WHERE `id`=3822";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1756481827.434774;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11329792;}}}";s:9:"profiling";s:84472:"a:3:{s:6:"memory";i:11479440;s:4:"time";d:0.20732808113098145;s:8:"messages";a:130:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1756481827.275407;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5542152;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1756481827.284613;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5585456;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.284638;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5585240;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.286415;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5597872;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.28647;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5600072;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.286974;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5602648;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.28777;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5620920;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.28971;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5623448;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.293281;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6090080;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.293739;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6092448;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.305356;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7033768;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.307034;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7043200;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.307099;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045400;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.307705;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047312;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.307896;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045952;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.309121;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047808;}i:40;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/comments', '1', '::1', 0, 'GET', '2025-08-29 16:37:07')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756481827.32255;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7710848;}i:41;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/comments', '1', '::1', 0, 'GET', '2025-08-29 16:37:07')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756481827.326614;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7712640;}i:44;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.327478;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7769920;}i:45;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.330016;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7781768;}i:47;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.330256;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7784720;}i:48;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.331451;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7787904;}i:50;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.332616;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7788512;}i:51;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.33589;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7793040;}i:53;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.336076;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7793448;}i:54;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.33664;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7796000;}i:56;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.339401;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8449224;}i:57;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.340668;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8454864;}i:59;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.340705;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8458568;}i:60;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.341256;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8460976;}i:62;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.341324;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8463016;}i:63;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.342447;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8465624;}i:65;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.342517;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8468152;}i:66;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.342823;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8470440;}i:68;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.343993;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8540336;}i:69;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.34449;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8542208;}i:71;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.345538;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8590192;}i:72;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.348462;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8597664;}i:74;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.348588;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8599872;}i:75;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.350431;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8602176;}i:77;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.350525;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8600664;}i:78;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.35192;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8604296;}i:80;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.352017;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8606056;}i:81;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.352284;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8608568;}i:83;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.353554;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8745160;}i:84;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.354044;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8749184;}i:86;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.354122;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8759304;}i:87;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.355418;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8768040;}i:89;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.355444;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8771000;}i:90;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.355884;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8773544;}i:92;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.355974;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8772440;}i:93;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.357374;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8775904;}i:95;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.357493;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8777968;}i:96;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.357794;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8780480;}i:98;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.357901;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8788528;}i:99;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.358181;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8791040;}i:101;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.358266;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8799224;}i:102;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.358778;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8806256;}i:104;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.358828;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8816520;}i:105;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.360056;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8825272;}i:107;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.360075;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8828232;}i:108;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.360556;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8830912;}i:110;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.360642;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8829656;}i:111;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.361981;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8833656;}i:113;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.36442;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8924832;}i:114;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.366108;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8931800;}i:116;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.367136;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8940360;}i:117;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.369114;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8947320;}i:119;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.369185;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8950912;}i:120;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.369759;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8953320;}i:122;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.369853;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8952688;}i:123;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.371308;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8956136;}i:125;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.371621;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9016968;}i:126;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.372154;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9018496;}i:128;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.372232;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9021936;}i:129;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.372843;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9029200;}i:131;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.373346;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9041736;}i:132;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.373959;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9048320;}i:134;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.374024;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9059192;}i:135;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.375338;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9067816;}i:137;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.375371;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9070776;}i:138;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.375938;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9073328;}i:140;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.376019;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9072312;}i:141;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.377521;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9075776;}i:143;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.377878;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9141256;}i:144;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.378421;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9148224;}i:146;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.378899;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9218392;}i:147;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.379407;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9220904;}i:149;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.379987;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9230216;}i:150;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.380915;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9232728;}i:152;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.381219;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9241824;}i:153;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.38183;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9244336;}i:155;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.382129;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9259064;}i:156;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.383162;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9261576;}i:158;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.383412;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9270672;}i:159;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.383964;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9273184;}i:161;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.384142;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9282280;}i:162;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.384612;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9284792;}i:164;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.384722;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9293888;}i:165;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.385156;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9296400;}i:167;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.385265;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9305496;}i:168;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.385693;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9308008;}i:170;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.385812;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9317104;}i:171;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.386251;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9319616;}i:173;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.386362;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9328872;}i:174;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.386817;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9331384;}i:177;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.407274;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:91;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10494680;}i:178;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.407962;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:91;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10496208;}i:180;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.409071;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10576448;}i:181;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.410736;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10587336;}i:183;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.410776;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10590296;}i:184;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.411486;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10592848;}i:186;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.411771;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10590888;}i:187;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.414223;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10594352;}i:192;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.4212;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11072136;}i:193;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.422024;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11073872;}i:195;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.422112;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11077176;}i:196;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.422654;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11080736;}i:198;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.422708;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11086072;}i:199;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.424132;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11094376;}i:201;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.424169;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11098728;}i:202;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.424722;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11101528;}i:204;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.424815;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11101120;}i:205;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.426166;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11104968;}i:211;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.428564;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11201232;}i:212;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.429748;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11233856;}i:217;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/comments'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.433994;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11328096;}i:218;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/comments'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.434662;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11329248;}i:220;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.20237016677856, `memory_max`=11479440 WHERE `id`=3822";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756481827.434782;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11331136;}i:221;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.20237016677856, `memory_max`=11479440 WHERE `id`=3822";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756481827.437912;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11332536;}}}";s:2:"db";s:83701:"a:1:{s:8:"messages";a:128:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.284638;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5585240;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.286415;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5597872;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.28647;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5600072;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.286974;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5602648;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.28777;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5620920;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.28971;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5623448;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.293281;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6090080;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.293739;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6092448;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.305356;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7033768;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.307034;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7043200;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.307099;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045400;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.307705;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047312;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.307896;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045952;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.309121;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047808;}i:40;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/comments', '1', '::1', 0, 'GET', '2025-08-29 16:37:07')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756481827.32255;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7710848;}i:41;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/comments', '1', '::1', 0, 'GET', '2025-08-29 16:37:07')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756481827.326614;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7712640;}i:44;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.327478;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7769920;}i:45;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.330016;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7781768;}i:47;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.330256;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7784720;}i:48;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.331451;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7787904;}i:50;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.332616;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7788512;}i:51;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.33589;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7793040;}i:53;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.336076;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7793448;}i:54;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.33664;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7796000;}i:56;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.339401;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8449224;}i:57;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.340668;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8454864;}i:59;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.340705;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8458568;}i:60;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.341256;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8460976;}i:62;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.341324;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8463016;}i:63;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.342447;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8465624;}i:65;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.342517;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8468152;}i:66;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.342823;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8470440;}i:68;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.343993;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8540336;}i:69;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.34449;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8542208;}i:71;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.345538;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8590192;}i:72;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.348462;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8597664;}i:74;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.348588;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8599872;}i:75;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.350431;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8602176;}i:77;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.350525;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8600664;}i:78;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.35192;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8604296;}i:80;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.352017;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8606056;}i:81;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.352284;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8608568;}i:83;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.353554;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8745160;}i:84;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.354044;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8749184;}i:86;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.354122;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8759304;}i:87;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.355418;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8768040;}i:89;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.355444;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8771000;}i:90;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.355884;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8773544;}i:92;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.355974;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8772440;}i:93;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.357374;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8775904;}i:95;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.357493;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8777968;}i:96;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.357794;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8780480;}i:98;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.357901;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8788528;}i:99;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.358181;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8791040;}i:101;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.358266;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8799224;}i:102;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.358778;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8806256;}i:104;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.358828;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8816520;}i:105;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.360056;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8825272;}i:107;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.360075;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8828232;}i:108;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.360556;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8830912;}i:110;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.360642;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8829656;}i:111;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.361981;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8833656;}i:113;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.36442;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8924832;}i:114;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.366108;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8931800;}i:116;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.367136;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8940360;}i:117;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.369114;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8947320;}i:119;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.369185;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8950912;}i:120;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.369759;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8953320;}i:122;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.369853;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8952688;}i:123;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.371308;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8956136;}i:125;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.371621;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9016968;}i:126;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.372154;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9018496;}i:128;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.372232;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9021936;}i:129;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.372843;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9029200;}i:131;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.373346;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9041736;}i:132;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.373959;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9048320;}i:134;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.374024;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9059192;}i:135;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.375338;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9067816;}i:137;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.375371;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9070776;}i:138;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.375938;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9073328;}i:140;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.376019;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9072312;}i:141;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.377521;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9075776;}i:143;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.377878;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9141256;}i:144;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.378421;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9148224;}i:146;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.378899;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9218392;}i:147;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.379407;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9220904;}i:149;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.379987;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9230216;}i:150;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.380915;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9232728;}i:152;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.381219;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9241824;}i:153;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.38183;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9244336;}i:155;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.382129;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9259064;}i:156;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.383162;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9261576;}i:158;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.383412;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9270672;}i:159;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.383964;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9273184;}i:161;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.384142;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9282280;}i:162;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.384612;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9284792;}i:164;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.384722;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9293888;}i:165;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.385156;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9296400;}i:167;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.385265;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9305496;}i:168;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.385693;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9308008;}i:170;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.385812;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9317104;}i:171;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.386251;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9319616;}i:173;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.386362;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9328872;}i:174;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.386817;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9331384;}i:177;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.407274;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:91;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10494680;}i:178;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.407962;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:91;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10496208;}i:180;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.409071;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10576448;}i:181;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.410736;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10587336;}i:183;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.410776;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10590296;}i:184;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.411486;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10592848;}i:186;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.411771;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10590888;}i:187;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.414223;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:328;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10594352;}i:192;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.4212;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11072136;}i:193;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.422024;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11073872;}i:195;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.422112;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11077176;}i:196;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.422654;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11080736;}i:198;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.422708;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11086072;}i:199;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.424132;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11094376;}i:201;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.424169;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11098728;}i:202;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.424722;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11101528;}i:204;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.424815;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11101120;}i:205;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.426166;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11104968;}i:211;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.428564;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11201232;}i:212;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.429748;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11233856;}i:217;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/comments'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.433994;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11328096;}i:218;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/comments'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756481827.434662;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11329248;}i:220;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.20237016677856, `memory_max`=11479440 WHERE `id`=3822";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756481827.434782;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11331136;}i:221;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.20237016677856, `memory_max`=11479440 WHERE `id`=3822";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756481827.437912;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11332536;}}}";s:5:"event";s:38321:"a:219:{i:0;a:5:{s:4:"time";d:1756481827.272432;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1756481827.284604;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1756481827.294174;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:1756481827.294202;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:1756481827.301833;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:1756481827.317982;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1756481827.320961;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:1756481827.32098;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:1756481827.322179;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:1756481827.326873;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:1756481827.326881;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:1756481827.327125;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"backend\controllers\DocumentController";}i:12;a:5:{s:4:"time";d:1756481827.327438;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:13;a:5:{s:4:"time";d:1756481827.338997;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:14;a:5:{s:4:"time";d:1756481827.339349;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:15;a:5:{s:4:"time";d:1756481827.342862;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:16;a:5:{s:4:"time";d:1756481827.342879;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:17;a:5:{s:4:"time";d:1756481827.343163;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:18;a:5:{s:4:"time";d:1756481827.34517;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:19;a:5:{s:4:"time";d:1756481827.345481;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:20;a:5:{s:4:"time";d:1756481827.351955;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:21;a:5:{s:4:"time";d:1756481827.352339;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:22;a:5:{s:4:"time";d:1756481827.352788;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:23;a:5:{s:4:"time";d:1756481827.353032;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:24;a:5:{s:4:"time";d:1756481827.353044;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:25;a:5:{s:4:"time";d:1756481827.35305;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:26;a:5:{s:4:"time";d:1756481827.353276;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:27;a:5:{s:4:"time";d:1756481827.353287;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:28;a:5:{s:4:"time";d:1756481827.353465;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:29;a:5:{s:4:"time";d:1756481827.354088;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:30;a:5:{s:4:"time";d:1756481827.357409;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:31;a:5:{s:4:"time";d:1756481827.357827;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:32;a:5:{s:4:"time";d:1756481827.357851;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:33;a:5:{s:4:"time";d:1756481827.358198;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:34;a:5:{s:4:"time";d:1756481827.358216;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:35;a:5:{s:4:"time";d:1756481827.358804;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:36;a:5:{s:4:"time";d:1756481827.362239;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:37;a:5:{s:4:"time";d:1756481827.36243;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:38;a:5:{s:4:"time";d:1756481827.362477;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:39;a:5:{s:4:"time";d:1756481827.362519;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:40;a:5:{s:4:"time";d:1756481827.362558;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:41;a:5:{s:4:"time";d:1756481827.362611;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:42;a:5:{s:4:"time";d:1756481827.362647;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:43;a:5:{s:4:"time";d:1756481827.362674;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:44;a:5:{s:4:"time";d:1756481827.362736;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:45;a:5:{s:4:"time";d:1756481827.364141;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:46;a:5:{s:4:"time";d:1756481827.366453;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:47;a:5:{s:4:"time";d:1756481827.371368;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:48;a:5:{s:4:"time";d:1756481827.371385;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:49;a:5:{s:4:"time";d:1756481827.3714;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:50;a:5:{s:4:"time";d:1756481827.371411;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:51;a:5:{s:4:"time";d:1756481827.371428;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:52;a:5:{s:4:"time";d:1756481827.371438;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:53;a:5:{s:4:"time";d:1756481827.371454;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:54;a:5:{s:4:"time";d:1756481827.371465;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:55;a:5:{s:4:"time";d:1756481827.371474;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:56;a:5:{s:4:"time";d:1756481827.371481;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:57;a:5:{s:4:"time";d:1756481827.371484;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:58;a:5:{s:4:"time";d:1756481827.371486;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:59;a:5:{s:4:"time";d:1756481827.371488;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:60;a:5:{s:4:"time";d:1756481827.37149;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:61;a:5:{s:4:"time";d:1756481827.371492;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:62;a:5:{s:4:"time";d:1756481827.371494;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:63;a:5:{s:4:"time";d:1756481827.371496;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:64;a:5:{s:4:"time";d:1756481827.371498;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:65;a:5:{s:4:"time";d:1756481827.3715;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:66;a:5:{s:4:"time";d:1756481827.371533;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:67;a:5:{s:4:"time";d:1756481827.371535;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:68;a:5:{s:4:"time";d:1756481827.371538;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:69;a:5:{s:4:"time";d:1756481827.37154;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:70;a:5:{s:4:"time";d:1756481827.371542;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:71;a:5:{s:4:"time";d:1756481827.371544;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:72;a:5:{s:4:"time";d:1756481827.371546;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:73;a:5:{s:4:"time";d:1756481827.371548;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:74;a:5:{s:4:"time";d:1756481827.37155;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:75;a:5:{s:4:"time";d:1756481827.371552;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:76;a:5:{s:4:"time";d:1756481827.372889;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:77;a:5:{s:4:"time";d:1756481827.373289;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:78;a:5:{s:4:"time";d:1756481827.373997;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:79;a:5:{s:4:"time";d:1756481827.377579;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:80;a:5:{s:4:"time";d:1756481827.377598;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:81;a:5:{s:4:"time";d:1756481827.37762;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:82;a:5:{s:4:"time";d:1756481827.377632;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:83;a:5:{s:4:"time";d:1756481827.377644;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:84;a:5:{s:4:"time";d:1756481827.377654;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:85;a:5:{s:4:"time";d:1756481827.377667;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:86;a:5:{s:4:"time";d:1756481827.37768;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:87;a:5:{s:4:"time";d:1756481827.377741;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:88;a:5:{s:4:"time";d:1756481827.377774;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:89;a:5:{s:4:"time";d:1756481827.378535;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:90;a:5:{s:4:"time";d:1756481827.378577;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:91;a:5:{s:4:"time";d:1756481827.378595;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:92;a:5:{s:4:"time";d:1756481827.378607;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:93;a:5:{s:4:"time";d:1756481827.37862;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:94;a:5:{s:4:"time";d:1756481827.378632;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:95;a:5:{s:4:"time";d:1756481827.378644;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:96;a:5:{s:4:"time";d:1756481827.378655;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:97;a:5:{s:4:"time";d:1756481827.378675;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:98;a:5:{s:4:"time";d:1756481827.378685;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:99;a:5:{s:4:"time";d:1756481827.378693;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:100;a:5:{s:4:"time";d:1756481827.378695;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:101;a:5:{s:4:"time";d:1756481827.378697;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:102;a:5:{s:4:"time";d:1756481827.378699;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:103;a:5:{s:4:"time";d:1756481827.378702;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:104;a:5:{s:4:"time";d:1756481827.378704;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:105;a:5:{s:4:"time";d:1756481827.378706;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:106;a:5:{s:4:"time";d:1756481827.378708;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:107;a:5:{s:4:"time";d:1756481827.378711;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:108;a:5:{s:4:"time";d:1756481827.378713;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:109;a:5:{s:4:"time";d:1756481827.378751;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:110;a:5:{s:4:"time";d:1756481827.378753;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:111;a:5:{s:4:"time";d:1756481827.378755;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:112;a:5:{s:4:"time";d:1756481827.378758;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:113;a:5:{s:4:"time";d:1756481827.37876;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:114;a:5:{s:4:"time";d:1756481827.378762;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:115;a:5:{s:4:"time";d:1756481827.378764;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:116;a:5:{s:4:"time";d:1756481827.378766;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:117;a:5:{s:4:"time";d:1756481827.378769;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:118;a:5:{s:4:"time";d:1756481827.378771;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:119;a:5:{s:4:"time";d:1756481827.378812;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:120;a:5:{s:4:"time";d:1756481827.379544;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:121;a:5:{s:4:"time";d:1756481827.379675;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:122;a:5:{s:4:"time";d:1756481827.379843;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:123;a:5:{s:4:"time";d:1756481827.380985;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:124;a:5:{s:4:"time";d:1756481827.381035;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:125;a:5:{s:4:"time";d:1756481827.381082;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:126;a:5:{s:4:"time";d:1756481827.381899;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:127;a:5:{s:4:"time";d:1756481827.381948;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:128;a:5:{s:4:"time";d:1756481827.381994;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:129;a:5:{s:4:"time";d:1756481827.383233;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:130;a:5:{s:4:"time";d:1756481827.383281;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:131;a:5:{s:4:"time";d:1756481827.383331;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:132;a:5:{s:4:"time";d:1756481827.384003;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:133;a:5:{s:4:"time";d:1756481827.384032;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:134;a:5:{s:4:"time";d:1756481827.38406;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:135;a:5:{s:4:"time";d:1756481827.384637;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:136;a:5:{s:4:"time";d:1756481827.384655;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:137;a:5:{s:4:"time";d:1756481827.384673;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:138;a:5:{s:4:"time";d:1756481827.385181;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:139;a:5:{s:4:"time";d:1756481827.385199;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:140;a:5:{s:4:"time";d:1756481827.385216;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:141;a:5:{s:4:"time";d:1756481827.385717;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:142;a:5:{s:4:"time";d:1756481827.385736;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:143;a:5:{s:4:"time";d:1756481827.385753;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:144;a:5:{s:4:"time";d:1756481827.386276;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:145;a:5:{s:4:"time";d:1756481827.386294;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:146;a:5:{s:4:"time";d:1756481827.386312;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:147;a:5:{s:4:"time";d:1756481827.386842;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:148;a:5:{s:4:"time";d:1756481827.38686;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:149;a:5:{s:4:"time";d:1756481827.388063;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:150;a:5:{s:4:"time";d:1756481827.393419;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:151;a:5:{s:4:"time";d:1756481827.406906;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:152;a:5:{s:4:"time";d:1756481827.407163;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:153;a:5:{s:4:"time";d:1756481827.407187;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:154;a:5:{s:4:"time";d:1756481827.408763;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:155;a:5:{s:4:"time";d:1756481827.414297;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentSignature";}i:156;a:5:{s:4:"time";d:1756481827.41451;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:157;a:5:{s:4:"time";d:1756481827.414872;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:158;a:5:{s:4:"time";d:1756481827.414923;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:159;a:5:{s:4:"time";d:1756481827.414977;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:160;a:5:{s:4:"time";d:1756481827.415004;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:161;a:5:{s:4:"time";d:1756481827.41501;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:162;a:5:{s:4:"time";d:1756481827.415612;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:163;a:5:{s:4:"time";d:1756481827.415628;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:164;a:5:{s:4:"time";d:1756481827.415679;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:165;a:5:{s:4:"time";d:1756481827.420446;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:166;a:5:{s:4:"time";d:1756481827.420485;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:167;a:5:{s:4:"time";d:1756481827.42053;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:168;a:5:{s:4:"time";d:1756481827.420898;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:169;a:5:{s:4:"time";d:1756481827.420903;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:170;a:5:{s:4:"time";d:1756481827.421115;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:171;a:5:{s:4:"time";d:1756481827.42205;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:172;a:5:{s:4:"time";d:1756481827.422674;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:173;a:5:{s:4:"time";d:1756481827.426209;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:174;a:5:{s:4:"time";d:1756481827.42622;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:175;a:5:{s:4:"time";d:1756481827.426227;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:176;a:5:{s:4:"time";d:1756481827.42623;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:177;a:5:{s:4:"time";d:1756481827.426232;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:178;a:5:{s:4:"time";d:1756481827.426402;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:179;a:5:{s:4:"time";d:1756481827.426845;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:180;a:5:{s:4:"time";d:1756481827.426854;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:181;a:5:{s:4:"time";d:1756481827.427088;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:182;a:5:{s:4:"time";d:1756481827.427092;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:183;a:5:{s:4:"time";d:1756481827.427146;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:184;a:5:{s:4:"time";d:1756481827.427314;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:185;a:5:{s:4:"time";d:1756481827.427319;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:186;a:5:{s:4:"time";d:1756481827.427334;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:187;a:5:{s:4:"time";d:1756481827.427377;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:188;a:5:{s:4:"time";d:1756481827.427653;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:189;a:5:{s:4:"time";d:1756481827.427657;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:190;a:5:{s:4:"time";d:1756481827.427703;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:191;a:5:{s:4:"time";d:1756481827.428212;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:192;a:5:{s:4:"time";d:1756481827.431536;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:193;a:5:{s:4:"time";d:1756481827.431548;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:194;a:5:{s:4:"time";d:1756481827.431786;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:195;a:5:{s:4:"time";d:1756481827.431807;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:196;a:5:{s:4:"time";d:1756481827.431812;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:197;a:5:{s:4:"time";d:1756481827.431817;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:198;a:5:{s:4:"time";d:1756481827.431892;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:199;a:5:{s:4:"time";d:1756481827.432494;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:200;a:5:{s:4:"time";d:1756481827.432507;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:201;a:5:{s:4:"time";d:1756481827.43255;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:202;a:5:{s:4:"time";d:1756481827.432561;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:203;a:5:{s:4:"time";d:1756481827.432625;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:204;a:5:{s:4:"time";d:1756481827.432756;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:205;a:5:{s:4:"time";d:1756481827.432795;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:206;a:5:{s:4:"time";d:1756481827.432909;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:207;a:5:{s:4:"time";d:1756481827.433357;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:208;a:5:{s:4:"time";d:1756481827.433544;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:209;a:5:{s:4:"time";d:1756481827.433691;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:210;a:5:{s:4:"time";d:1756481827.433706;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"backend\controllers\DocumentController";}i:211;a:5:{s:4:"time";d:1756481827.433915;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:212;a:5:{s:4:"time";d:1756481827.434678;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:213;a:5:{s:4:"time";d:1756481827.434718;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:214;a:5:{s:4:"time";d:1756481827.437956;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:215;a:5:{s:4:"time";d:1756481827.437964;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:216;a:5:{s:4:"time";d:1756481827.43797;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:217;a:5:{s:4:"time";d:1756481827.438431;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:218;a:5:{s:4:"time";d:1756481827.43864;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1756481827.232324;s:3:"end";d:1756481827.440194;s:6:"memory";i:11509960;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:320:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1756481827.317992;i:4;a:0:{}i:5;i:7223736;}}s:5:"route";s:17:"document/comments";s:6:"action";s:56:"backend\controllers\DocumentController::actionComments()";}";s:7:"request";s:10990:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:18:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:7:"referer";s:53:"http://localhost:8005/backoffice/index.php?r=document";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:957:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=kg5944qo99nlrpbhm7q5konqli; _csrf-backend=917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22TwezWto5toyW6sW07LndJjdZNt6UE7DV%22%3B%7D; advanced-frontend-fmz=moh7rs0lensqa7tdlnifptpb87; _csrf-frontend=dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68b1c9234a379";s:16:"X-Debug-Duration";s:3:"207";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68b1c9234a379";s:10:"Set-Cookie";s:311:"_identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; expires=Sun, 28 Sep 2025 15:37:07 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:17:"document/comments";s:6:"action";s:56:"backend\controllers\DocumentController::actionComments()";s:12:"actionParams";a:1:{s:2:"id";s:2:"18";}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:104:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-64c99400-8dd8-46a7-9d6f-a3bc9d13252c";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:49:"/backoffice/index.php?r=document%2Fcomments&id=18";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"63616";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:27:"r=document%2Fcomments&id=18";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:12:"HTTP_REFERER";s:53:"http://localhost:8005/backoffice/index.php?r=document";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:957:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=kg5944qo99nlrpbhm7q5konqli; _csrf-backend=917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22TwezWto5toyW6sW07LndJjdZNt6UE7DV%22%3B%7D; advanced-frontend-fmz=moh7rs0lensqa7tdlnifptpb87; _csrf-frontend=dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:1756481827.219901;s:12:"REQUEST_TIME";i:1756481827;}s:3:"GET";a:2:{s:1:"r";s:17:"document/comments";s:2:"id";s:2:"18";}s:4:"POST";a:0:{}s:6:"COOKIE";a:7:{s:18:"_identity-frontend";s:158:"ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"_identity-backend";s:157:"39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:20:"advanced-backend-fmz";s:26:"kg5944qo99nlrpbhm7q5konqli";s:13:"_csrf-backend";s:139:"917abb8469aa4d698f81356ac859964e5a8cac9582dcc38b00411aefe9ca5df9a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"TwezWto5toyW6sW07LndJjdZNt6UE7DV";}";s:21:"advanced-frontend-fmz";s:26:"moh7rs0lensqa7tdlnifptpb87";s:14:"_csrf-frontend";s:140:"dd73e6ec16db6a655690a449fe4e4fa789f76c9c2c95dfa96614b330565202a0a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"ca_8DpXaaaTHYgxFa4WS3IK6vVsvuynG";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:7895:"a:17:{s:30:"yii\validators\ValidationAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:17:"yii.validation.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\Reclassering/vendor/bower-asset/jquery/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\8bc86ca8";s:7:"baseUrl";s:27:"/backoffice/assets/8bc86ca8";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:27:"yii\widgets\ActiveFormAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:17:"yii.activeForm.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap5\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap5\BootstrapAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:41:"hail812\adminlte3\assets\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:74:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins/fontawesome-free";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\9a5ee6fe";s:7:"baseUrl";s:27:"/backoffice/assets/9a5ee6fe";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:34:"hail812\adminlte3\assets\BaseAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";N;s:7:"baseUrl";N;s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";i:2;s:35:"yii\bootstrap4\BootstrapPluginAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:36:"hail812\adminlte3\assets\PluginAsset";a:9:{s:10:"sourcePath";s:57:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\834b4d89";s:7:"baseUrl";s:27:"/backoffice/assets/834b4d89";s:7:"depends";a:1:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:46:"/backoffice/assets/e4a6bb80/control_sidebar.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:39:"\hail812\adminlte3\assets\AdminLteAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:45:"backoffice/assets/e4a6bb80/control_sidebar.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:39:"\hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:23:"backend\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:11:"/backoffice";s:7:"depends";a:7:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:19:"yii\web\JqueryAsset";i:3;s:16:"yii\jui\JuiAsset";i:4;s:29:"kartik\sortable\SortableAsset";i:5;s:38:"hail812\adminlte3\assets\AdminLteAsset";i:6;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:3:{i:0;s:50:"https://unpkg.com/lucide@latest/dist/umd/lucide.js";i:1;s:10:"js/main.js";i:2;s:75:"https://cdn.jsdelivr.net/npm/sweetalert2@11.7.1/dist/sweetalert2.all.min.js";}s:3:"css";a:1:{i:0;s:12:"css/site.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\jui\JuiAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\Reclassering/vendor/bower-asset/jquery-ui";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a78fe255";s:7:"baseUrl";s:27:"/backoffice/assets/a78fe255";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:12:"jquery-ui.js";}s:3:"css";a:1:{i:0;s:31:"themes/smoothness/jquery-ui.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"kartik\sortable\SortableAsset";a:17:{s:10:"sourcePath";s:60:"C:\Web\Reclassering\vendor\kartik-v\yii2-sortable\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\3202e5a2";s:7:"baseUrl";s:27:"/backoffice/assets/3202e5a2";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:2:{i:0;s:19:"js/html5sortable.js";i:1;s:23:"js/kv-html5-sortable.js";}s:3:"css";a:1:{i:0;s:25:"css/kv-html5-sortable.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68b1c9234a379";s:3:"url";s:70:"http://localhost:8005/backoffice/index.php?r=document%2Fcomments&id=18";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:1756481827.219901;s:10:"statusCode";i:200;s:8:"sqlCount";i:64;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11479440;s:14:"processingTime";d:0.20732808113098145;}s:10:"exceptions";a:0:{}}