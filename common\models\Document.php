<?php

namespace common\models;

use common\components\SignatureHelper;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\behaviors\BlameableBehavior;
use yii\db\Expression;
use Twig\Loader\ArrayLoader;
use Twig\Environment;
use yii\web\NotFoundHttpException;

/**
 * This is the model class for table "Document".
 *
 * @property int $id
 * @property int $documenttype_id
 * @property int $gedetineerde_id
 * @property int $mark_final
 * @property string $status
 * @property string $file_path
 * @property string $filename
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string $created_at
 * @property string $updated_at
 *
 * @property User $createdBy
 * @property DocumentType $documenttype
 * @property DocumentAntwoord[] $DocumentAntwoords
 * @property DocumentFeedback[] $DocumentFeedbacks
 * @property User $updatedBy
 */
class Document extends \yii\db\ActiveRecord
{

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'document';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new Expression('CURRENT_TIMESTAMP'),
            ],
            [
                'class' => BlameableBehavior::class,
                'createdByAttribute' => 'created_by',
                'updatedByAttribute' => 'updated_by',
                'value' => function () {
                    return Yii::$app->user->id;
                }
            ],
            [
                'class' => \raoul2000\workflow\base\SimpleWorkflowBehavior::className(),
                'defaultWorkflowId' => 'document-workflow',
                'propagateErrorsToModel' => true,
                'statusAttribute' => 'status'
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['created_by', 'updated_by', 'file_path'], 'default', 'value' => null],
            [['status'], 'default', 'value' => 'medewerker'],
            [['documenttype_id', 'filename'], 'required'],
            [['documenttype_id', 'created_by', 'updated_by', 'gedetineerde_id', 'mark_final'], 'integer'],
            [['created_at', 'updated_at'], 'safe'],
            [['filename', 'file_path'], 'string', 'max' => 255],
            [['status'], 'string', 'max' => 100],
            [['created_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['created_by' => 'id']],
            [['documenttype_id'], 'exist', 'skipOnError' => true, 'targetClass' => DocumentType::class, 'targetAttribute' => ['documenttype_id' => 'id']],
            [['updated_by'], 'exist', 'skipOnError' => true, 'targetClass' => User::class, 'targetAttribute' => ['updated_by' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'documenttype_id' => 'Document Type',
            'status' => 'Status',
            'mark_final' => 'Finale versie',
            'filename' => 'Document Naam',
            'gedetineerde_id' => 'Gedetineerde',
            'file_path' => 'Pdf',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[CreatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCreatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'created_by']);
    }

    /**
     * Gets query for [[Gedetineerde]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getGedetineerde()
    {
        return $this->hasOne(Gedetineerde::class, ['id' => 'gedetineerde_id']);
    }

    /**
     * Gets query for [[Documentnaam]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getDocumentType()
    {
        return $this->hasOne(DocumentType::class, ['id' => 'documenttype_id']);
    }

    /**
     * Gets query for [[DocumentAntwoords]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getDocumentAntwoords()
    {
        return $this->hasMany(DocumentAntwoord::class, ['document_id' => 'id']);
    }

    /**
     * Gets query for [[DocumentFeedbacks]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getDocumentFeedbacks()
    {
        return $this->hasMany(DocumentFeedback::class, ['document_id' => 'id']);
    }

    /**
     * Gets query for [[UpdatedBy]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUpdatedBy()
    {
        return $this->hasOne(User::class, ['id' => 'updated_by']);
    }

    public function getDocumentSignatures()
    {
        return $this->hasMany(DocumentSignature::class, ['document_id' => 'id']);
    }

    /**
     * {@inheritdoc}
     */
    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }

        // Convert filename to uppercase
        $this->filename = strtoupper($this->filename);

        return true;
    }

    public function renderWithLayout($id, $signatures = [])
    {
        $data = getDocument($id);

        if (!$data) {
            throw new NotFoundHttpException('data not found.');
        } else {
            $data = $data['data'];
        }

        // Decrypt signatures
        foreach ($data['signatures'] as &$signature) {
            $signature['signature'] = SignatureHelper::decodeAndDecrypt(
                $signature['signature'],
                $signature['user']['id']
            );
        }
        unset($signature); // break the reference
        // dd($data);

        $currentDate = date('d-m-Y');

        $loader = new ArrayLoader([
            'template' => $this->documentType->layout_template,
        ]);

        $twig = new Environment($loader);


        return $twig->render('template', [
            'document' => $this,
            'data' => $data,
            'currentDate' => $currentDate,
        ]);
    }
}
