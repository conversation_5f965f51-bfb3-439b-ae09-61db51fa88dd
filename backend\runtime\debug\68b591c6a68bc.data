a:14:{s:6:"config";s:15911:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:71:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}s:24:"rmrevin/yii2-fontawesome";a:3:{s:4:"name";s:24:"rmrevin/yii2-fontawesome";s:7:"version";s:8:"2.10.3.0";s:5:"alias";a:1:{s:24:"@rmrevin/yii/fontawesome";s:51:"C:\Web\Reclassering\vendor/rmrevin/yii2-fontawesome";}}s:24:"edofre/yii2-fullcalendar";a:3:{s:4:"name";s:24:"edofre/yii2-fullcalendar";s:7:"version";s:8:"1.0.11.0";s:5:"alias";a:1:{s:20:"@edofre/fullcalendar";s:55:"C:\Web\Reclassering\vendor/edofre/yii2-fullcalendar/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:34:"miloschuman/yii2-highcharts-widget";a:3:{s:4:"name";s:34:"miloschuman/yii2-highcharts-widget";s:7:"version";s:8:"11.0.0.0";s:5:"alias";a:1:{s:23:"@miloschuman/highcharts";s:65:"C:\Web\Reclassering\vendor/miloschuman/yii2-highcharts-widget/src";}}}}";s:3:"log";s:52476:"a:1:{s:8:"messages";a:94:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756729798.586415;i:4;a:0:{}i:5;i:2898680;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756729798.587235;i:4;a:0:{}i:5;i:3003904;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1756729798.587246;i:4;a:0:{}i:5;i:3004200;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756729798.587652;i:4;a:0:{}i:5;i:3034200;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756729798.588035;i:4;a:0:{}i:5;i:3061672;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756729798.589041;i:4;a:0:{}i:5;i:3216704;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1756729798.589052;i:4;a:0:{}i:5;i:3217344;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1756729798.613059;i:4;a:0:{}i:5;i:4214960;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.643508;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5539056;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1756729798.643634;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5541336;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.649089;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5599152;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.651029;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5619872;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.661822;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6089688;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756729798.680576;i:4;a:0:{}i:5;i:6782208;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756729798.684004;i:4;a:0:{}i:5;i:6962304;}i:25;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1756729798.684041;i:4;a:0:{}i:5;i:6962944;}i:26;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.686541;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7032864;}i:29;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.689133;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7044496;}i:32;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.690145;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7044280;}i:35;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1756729798.70232;i:4;a:0:{}i:5;i:7224712;}i:37;a:6:{i:0;s:36:"Route requested: 'document/comments'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1756729798.702519;i:4;a:0:{}i:5;i:7223648;}i:38;a:6:{i:0;s:31:"Route to run: document/comments";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1756729798.707526;i:4;a:0:{}i:5;i:7439000;}i:39;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/comments', '1', '::1', 0, 'GET', '2025-09-01 13:29:58')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1756729798.718465;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7709352;}i:42;a:6:{i:0;s:72:"Running action: backend\controllers\DocumentController::actionComments()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1756729798.725282;i:4;a:0:{}i:5;i:7720800;}i:43;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.725883;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7768496;}i:46;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.729421;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7783296;}i:49;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.7305;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7786960;}i:52;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.732248;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7791808;}i:55;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.736058;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8447456;}i:58;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.739181;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8456800;}i:61;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.740471;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8461120;}i:64;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='onderhoofd')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.743376;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8464992;}i:67;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='onderhoofd')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.745448;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8538456;}i:70;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.749891;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8589176;}i:73;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.753087;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8598856;}i:76;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.75396;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8599520;}i:79;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.755538;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8604448;}i:82;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.757319;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8743552;}i:85;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.758508;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8757912;}i:88;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.761626;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8769608;}i:91;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.762229;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8770920;}i:94;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.765184;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8776360;}i:97;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.765861;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8786920;}i:100;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.766479;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8797784;}i:103;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.76723;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8815296;}i:106;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.769059;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8827008;}i:109;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.769848;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8828304;}i:112;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.772743;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8924648;}i:115;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.773945;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8939136;}i:118;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.77602;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8949688;}i:121;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.776957;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8951336;}i:124;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.77898;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9015528;}i:127;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.779719;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9038328;}i:130;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.780799;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9050656;}i:133;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.782226;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9064864;}i:136;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.782781;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9082536;}i:139;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.784172;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9094120;}i:142;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.785129;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9095528;}i:145;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.786996;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9165640;}i:148;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.788143;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9241840;}i:151;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.789261;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9259296;}i:154;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.790182;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9270904;}i:157;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.790927;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9282512;}i:160;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.791663;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9294120;}i:163;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.792701;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9305728;}i:166;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.793636;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9317336;}i:169;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.794418;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9328944;}i:172;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.794857;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9340552;}i:175;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.796059;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9352320;}i:178;a:6:{i:0;s:76:"Rendering view file: C:\Web\Reclassering\backend\views\document\comments.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756729798.798216;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9553864;}i:179;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.838274;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:113;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10534648;}i:182;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.839636;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:126;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10554408;}i:185;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.844324;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:126;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10865680;}i:188;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.846534;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11035304;}i:191;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.848532;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11049152;}i:194;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.849469;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11049616;}i:197;a:6:{i:0;s:93:"Rendering view file: C:\Web\Reclassering\backend\views\document-signature/_signature-form.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756729798.852484;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:385;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11053840;}i:198;a:6:{i:0;s:71:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756729798.853447;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11032496;}i:199;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\navbar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756729798.872578;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11373208;}i:200;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.875667;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11459192;}i:203;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.877042;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11464128;}i:206;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.877648;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11473880;}i:209;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.879403;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11485896;}i:212;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.880134;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11488800;}i:215;a:6:{i:0;s:79:"Rendering view file: C:\Web\Reclassering\common\widgets\views\notifications.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756729798.882124;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:27;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11491384;}i:216;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\profile.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756729798.882516;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\ProfileWidget.php";s:4:"line";i:32;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:155;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11500928;}i:217;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756729798.882681;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11464488;}i:218;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756729798.882853;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:49;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11510520;}i:219;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.883292;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11588392;}i:222;a:6:{i:0;s:74:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\content.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756729798.89133;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:52;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11639904;}i:223;a:6:{i:0;s:82:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\control-sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756729798.894227;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:56;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11745232;}i:224;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\backend\views\layouts\footer.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1756729798.895066;i:4;a:2:{i:0;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:60;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11747552;}i:225;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/comments'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.899494;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11766192;}i:228;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.32824611663818, `memory_max`=11946704 WHERE `id`=3992";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1756729798.900928;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11769016;}}}";s:9:"profiling";s:87540:"a:3:{s:6:"memory";i:11946704;s:4:"time";d:0.336806058883667;s:8:"messages";a:136:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1756729798.643673;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5542144;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1756729798.647117;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5585448;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.647144;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5585232;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.649034;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5597864;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.649103;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5600064;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.650028;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5602640;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.65106;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5620912;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.653077;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5623440;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.661894;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6090072;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.662783;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6092440;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.686609;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7033776;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.689062;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7043208;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.689154;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045408;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.690012;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047320;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.690159;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045960;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.692131;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047816;}i:40;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/comments', '1', '::1', 0, 'GET', '2025-09-01 13:29:58')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756729798.718534;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7710712;}i:41;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/comments', '1', '::1', 0, 'GET', '2025-09-01 13:29:58')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756729798.724447;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7712504;}i:44;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.725911;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7769784;}i:45;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.729341;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7781632;}i:47;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.729452;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7784584;}i:48;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.730123;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7787768;}i:50;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.730512;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7788376;}i:51;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.732165;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7792904;}i:53;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.732257;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7793312;}i:54;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.732733;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7795864;}i:56;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.736119;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8449120;}i:57;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.739066;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8454760;}i:59;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.739216;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8458464;}i:60;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.740269;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8460872;}i:62;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.740501;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8462912;}i:63;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.743189;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8465520;}i:65;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='onderhoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.743398;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8468048;}i:66;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='onderhoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.744115;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8470336;}i:68;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='onderhoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.745462;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8540232;}i:69;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='onderhoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.745975;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8542104;}i:71;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.749962;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8590088;}i:72;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.753016;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8597560;}i:74;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.75311;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8599768;}i:75;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.75388;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8602072;}i:77;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.753969;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8600560;}i:78;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.755443;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8604192;}i:80;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.755548;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8605952;}i:81;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.756081;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8608464;}i:83;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.757354;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8745056;}i:84;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.758239;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8749080;}i:86;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.758561;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8759200;}i:87;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.761462;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8767936;}i:89;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.761664;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8770896;}i:90;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.762127;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8773440;}i:92;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.762242;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8772336;}i:93;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.76501;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8775800;}i:95;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.765199;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8777864;}i:96;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.765736;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8780376;}i:98;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.765872;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8788424;}i:99;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.76638;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8791104;}i:101;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.766493;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8799288;}i:102;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.766973;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8806320;}i:104;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.767276;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8816584;}i:105;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.769021;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8825336;}i:107;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.769071;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8828296;}i:108;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.769737;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8830976;}i:110;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.769863;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8829720;}i:111;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.771897;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8833720;}i:113;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.772766;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8924896;}i:114;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.773646;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8931864;}i:116;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.774008;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8940424;}i:117;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.775983;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8947384;}i:119;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.776033;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8950976;}i:120;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.776866;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8953384;}i:122;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.77697;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8952752;}i:123;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.778582;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8956200;}i:125;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.778994;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9017032;}i:126;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.77955;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9019728;}i:128;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.779733;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9039832;}i:129;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.780233;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9048360;}i:131;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.780846;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9052160;}i:132;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.781537;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9053792;}i:134;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.782268;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9066368;}i:135;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.782711;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9072952;}i:137;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.782794;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9083824;}i:138;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.784013;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9092448;}i:140;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.784225;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9095408;}i:141;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.784984;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9097960;}i:143;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.785148;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9096944;}i:144;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.786569;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9100408;}i:146;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.787017;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9165888;}i:147;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.787645;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9172856;}i:149;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.788159;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9243024;}i:150;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.788651;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9245536;}i:152;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.789312;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9260480;}i:153;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.789991;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9262992;}i:155;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.790199;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9272088;}i:156;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.79074;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9274600;}i:158;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.790946;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9283696;}i:159;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.791472;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9286208;}i:161;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.79168;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9295304;}i:162;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.792164;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9297816;}i:164;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.79275;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9306912;}i:165;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.793442;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9309424;}i:167;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.793654;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9318520;}i:168;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.79422;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9321032;}i:170;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.794439;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9330128;}i:171;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.794753;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9332640;}i:173;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.794866;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9341736;}i:174;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.795429;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9344248;}i:176;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.796109;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9353504;}i:177;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.796995;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9356016;}i:180;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.838349;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:113;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10536152;}i:181;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.839412;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:113;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10538848;}i:183;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.839652;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:126;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10555912;}i:184;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.840236;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:126;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10558592;}i:186;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.844412;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:126;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10867184;}i:187;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.845012;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:126;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10869864;}i:189;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.846566;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11036592;}i:190;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.848485;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11047480;}i:192;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.848545;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11050440;}i:193;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.849363;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11052992;}i:195;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.849481;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11051032;}i:196;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.852197;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11054496;}i:201;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.875792;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11460968;}i:202;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.876847;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11462704;}i:204;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.87706;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11466008;}i:205;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.877587;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11469568;}i:207;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.877659;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11474904;}i:208;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.879347;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11483208;}i:210;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.879416;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11487560;}i:211;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.880021;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11490360;}i:213;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.880146;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11489952;}i:214;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.881888;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11493800;}i:220;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.883306;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11590064;}i:221;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.884036;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11622688;}i:226;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/comments'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.899551;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11767320;}i:227;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/comments'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.900644;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11768472;}i:229;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.32824611663818, `memory_max`=11946704 WHERE `id`=3992";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756729798.900953;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11770360;}i:230;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.32824611663818, `memory_max`=11946704 WHERE `id`=3992";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756729798.905028;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11771760;}}}";s:2:"db";s:86771:"a:1:{s:8:"messages";a:134:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.647144;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5585232;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.649034;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5597864;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.649103;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5600064;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.650028;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5602640;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.65106;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5620912;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.653077;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5623440;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.661894;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6090072;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.662783;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6092440;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.686609;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7033776;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.689062;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7043208;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.689154;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045408;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.690012;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047320;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.690159;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7045960;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.692131;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7047816;}i:40;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/comments', '1', '::1', 0, 'GET', '2025-09-01 13:29:58')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756729798.718534;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7710712;}i:41;a:6:{i:0;s:163:"INSERT INTO `audit_entry` (`route`, `user_id`, `ip`, `ajax`, `request_method`, `created`) VALUES ('document/comments', '1', '::1', 0, 'GET', '2025-09-01 13:29:58')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756729798.724447;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:168;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:57;s:8:"function";s:6:"record";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:386;s:8:"function";s:6:"create";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"::";}}i:5;i:7712504;}i:44;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.725911;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7769784;}i:45;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.729341;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7781632;}i:47;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.729452;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7784584;}i:48;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.730123;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7787768;}i:50;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.730512;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7788376;}i:51;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.732165;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7792904;}i:53;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.732257;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7793312;}i:54;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.732733;i:4;a:2:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:379;s:8:"function";s:9:"findModel";s:5:"class";s:38:"backend\controllers\DocumentController";s:4:"type";s:2:"->";}}i:5;i:7795864;}i:56;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.736119;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8449120;}i:57;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.739066;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8454760;}i:59;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.739216;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8458464;}i:60;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.740269;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8460872;}i:62;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.740501;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8462912;}i:63;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.743189;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8465520;}i:65;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='onderhoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.743398;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8468048;}i:66;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='onderhoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.744115;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8470336;}i:68;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='onderhoofd')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.745462;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8540232;}i:69;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='onderhoofd')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.745975;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:346;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:8542104;}i:71;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.749962;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8590088;}i:72;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.753016;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8597560;}i:74;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.75311;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8599768;}i:75;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_feedback`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.75388;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8602072;}i:77;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.753969;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8600560;}i:78;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_feedback' AND `kcu`.`TABLE_NAME` = 'document_feedback'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.755443;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:381;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8604192;}i:80;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.755548;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8605952;}i:81;a:6:{i:0;s:40:"SELECT * FROM `document` WHERE `id`='18'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.756081;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8608464;}i:83;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.757354;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8745056;}i:84;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.758239;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8749080;}i:86;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.758561;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8759200;}i:87;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.761462;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8767936;}i:89;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.761664;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8770896;}i:90;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.762127;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8773440;}i:92;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.762242;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8772336;}i:93;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.76501;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8775800;}i:95;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.765199;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8777864;}i:96;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.765736;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8780376;}i:98;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.765872;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8788424;}i:99;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.76638;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8791104;}i:101;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.766493;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8799288;}i:102;a:6:{i:0;s:56:"SELECT * FROM `document_antwoord` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.766973;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8806320;}i:104;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.767276;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8816584;}i:105;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.769021;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8825336;}i:107;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.769071;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8828296;}i:108;a:6:{i:0;s:37:"SHOW CREATE TABLE `document_antwoord`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.769737;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8830976;}i:110;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.769863;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8829720;}i:111;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_antwoord' AND `kcu`.`TABLE_NAME` = 'document_antwoord'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.771897;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8833720;}i:113;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.772766;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8924896;}i:114;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.773646;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8931864;}i:116;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.774008;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8940424;}i:117;a:6:{i:0;s:30:"SHOW FULL COLUMNS FROM `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.775983;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8947384;}i:119;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.776033;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8950976;}i:120;a:6:{i:0;s:25:"SHOW CREATE TABLE `vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.776866;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8953384;}i:122;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.77697;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8952752;}i:123;a:6:{i:0;s:766:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'vraag' AND `kcu`.`TABLE_NAME` = 'vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.778582;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:8956200;}i:125;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.778994;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9017032;}i:126;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.77955;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9019728;}i:128;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.779733;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9039832;}i:129;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.780233;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9048360;}i:131;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.780846;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9052160;}i:132;a:6:{i:0;s:57:"SELECT * FROM `document_signature` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.781537;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:115;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9053792;}i:134;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.782268;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9066368;}i:135;a:6:{i:0;s:80:"SELECT * FROM `documenttype_vraag` WHERE `document_id`=8 ORDER BY `vraag_nummer`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.782711;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9072952;}i:137;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.782794;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9083824;}i:138;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.784013;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9092448;}i:140;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.784225;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9095408;}i:141;a:6:{i:0;s:38:"SHOW CREATE TABLE `documenttype_vraag`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.784984;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9097960;}i:143;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.785148;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9096944;}i:144;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'documenttype_vraag' AND `kcu`.`TABLE_NAME` = 'documenttype_vraag'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.786569;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9100408;}i:146;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.787017;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9165888;}i:147;a:6:{i:0;s:75:"SELECT * FROM `vraag` WHERE `id` IN (18, 19, 20, 21, 22, 23, 24, 25, 26, 5)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.787645;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:130;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9172856;}i:149;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.788159;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9243024;}i:150;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.788651;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9245536;}i:152;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.789312;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9260480;}i:153;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.789991;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9262992;}i:155;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.790199;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9272088;}i:156;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.79074;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9274600;}i:158;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.790946;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9283696;}i:159;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.791472;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9286208;}i:161;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.79168;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9295304;}i:162;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.792164;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9297816;}i:164;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.79275;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9306912;}i:165;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.793442;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9309424;}i:167;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.793654;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9318520;}i:168;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.79422;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9321032;}i:170;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.794439;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9330128;}i:171;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.794753;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9332640;}i:173;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.794866;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9341736;}i:174;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.795429;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9344248;}i:176;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.796109;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9353504;}i:177;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.796995;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"C:\Web\Reclassering\common\helpers\functions.php";s:4:"line";i:156;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:3:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:383;s:8:"function";s:11:"getDocument";}}i:5;i:9356016;}i:180;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.838349;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:113;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10536152;}i:181;a:6:{i:0;s:56:"SELECT * FROM `document_feedback` WHERE `document_id`=18";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.839412;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:113;s:8:"function";s:7:"__isset";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10538848;}i:183;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.839652;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:126;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10555912;}i:184;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.840236;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:126;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10558592;}i:186;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.844412;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:126;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10867184;}i:187;a:6:{i:0;s:33:"SELECT * FROM `user` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.845012;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:126;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10869864;}i:189;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.846566;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11036592;}i:190;a:6:{i:0;s:43:"SHOW FULL COLUMNS FROM `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.848485;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11047480;}i:192;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.848545;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11050440;}i:193;a:6:{i:0;s:38:"SHOW CREATE TABLE `document_signature`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.849363;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11052992;}i:195;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.849481;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11051032;}i:196;a:6:{i:0;s:792:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_signature' AND `kcu`.`TABLE_NAME` = 'document_signature'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.852197;i:4;a:2:{i:0;a:5:{s:4:"file";s:55:"C:\Web\Reclassering\backend\views\document\comments.php";s:4:"line";i:386;s:8:"function";s:11:"__construct";s:5:"class";s:19:"yii\base\BaseObject";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\backend\controllers\DocumentController.php";s:4:"line";i:387;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11054496;}i:201;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.875792;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11460968;}i:202;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.876847;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11462704;}i:204;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.87706;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11466008;}i:205;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.877587;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11469568;}i:207;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.877659;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11474904;}i:208;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.879347;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11483208;}i:210;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.879416;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11487560;}i:211;a:6:{i:0;s:37:"SHOW CREATE TABLE `notification_user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.880021;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11490360;}i:213;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.880146;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11489952;}i:214;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'notification_user' AND `kcu`.`TABLE_NAME` = 'notification_user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.881888;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\backend\views\layouts\navbar.php";s:4:"line";i:153;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:50:"C:\Web\Reclassering\backend\views\layouts\main.php";s:4:"line";i:45;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}}i:5;i:11493800;}i:220;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.883306;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11590064;}i:221;a:6:{i:0;s:119:"SELECT * FROM `menu_item` WHERE (`visible`=1) AND ((`location`='backend') OR (`location`='both')) ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.884036;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\views\sidebar.php";s:4:"line";i:17;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:53:"C:\Web\Reclassering\backend\views\layouts\sidebar.php";s:4:"line";i:5;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11622688;}i:226;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/comments'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.899551;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11767320;}i:227;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/comments'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1756729798.900644;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11768472;}i:229;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.32824611663818, `memory_max`=11946704 WHERE `id`=3992";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1756729798.900953;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11770360;}i:230;a:6:{i:0;s:91:"UPDATE `audit_entry` SET `duration`=0.32824611663818, `memory_max`=11946704 WHERE `id`=3992";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1756729798.905028;i:4;a:2:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\models\AuditEntry.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:290;s:8:"function";s:8:"finalize";s:5:"class";s:37:"bedezign\yii2\audit\models\AuditEntry";s:4:"type";s:2:"->";}}i:5;i:11771760;}}}";s:5:"event";s:42358:"a:242:{i:0;a:5:{s:4:"time";d:1756729798.63466;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1756729798.647106;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1756729798.663208;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:1756729798.663246;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:1756729798.673826;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:1756729798.702418;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1756729798.711905;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:7;a:5:{s:4:"time";d:1756729798.711975;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:8;a:5:{s:4:"time";d:1756729798.716851;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:9;a:5:{s:4:"time";d:1756729798.724848;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:10;a:5:{s:4:"time";d:1756729798.724869;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:11;a:5:{s:4:"time";d:1756729798.725244;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"backend\controllers\DocumentController";}i:12;a:5:{s:4:"time";d:1756729798.725831;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:13;a:5:{s:4:"time";d:1756729798.734389;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:14;a:5:{s:4:"time";d:1756729798.73591;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:15;a:5:{s:4:"time";d:1756729798.744166;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:16;a:5:{s:4:"time";d:1756729798.744211;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:17;a:5:{s:4:"time";d:1756729798.744765;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:18;a:5:{s:4:"time";d:1756729798.748767;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:19;a:5:{s:4:"time";d:1756729798.749783;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:20;a:5:{s:4:"time";d:1756729798.75548;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:21;a:5:{s:4:"time";d:1756729798.756145;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:22;a:5:{s:4:"time";d:1756729798.756472;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:23;a:5:{s:4:"time";d:1756729798.756625;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:24;a:5:{s:4:"time";d:1756729798.756635;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:25;a:5:{s:4:"time";d:1756729798.75664;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:26;a:5:{s:4:"time";d:1756729798.756766;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:27;a:5:{s:4:"time";d:1756729798.756793;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:28;a:5:{s:4:"time";d:1756729798.757151;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:29;a:5:{s:4:"time";d:1756729798.758416;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:30;a:5:{s:4:"time";d:1756729798.765077;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:31;a:5:{s:4:"time";d:1756729798.765779;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:32;a:5:{s:4:"time";d:1756729798.765808;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:33;a:5:{s:4:"time";d:1756729798.766408;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:34;a:5:{s:4:"time";d:1756729798.766428;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:35;a:5:{s:4:"time";d:1756729798.767141;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:36;a:5:{s:4:"time";d:1756729798.771992;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:37;a:5:{s:4:"time";d:1756729798.772024;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:38;a:5:{s:4:"time";d:1756729798.772048;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:39;a:5:{s:4:"time";d:1756729798.772068;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:40;a:5:{s:4:"time";d:1756729798.772088;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:41;a:5:{s:4:"time";d:1756729798.772112;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:42;a:5:{s:4:"time";d:1756729798.772132;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:43;a:5:{s:4:"time";d:1756729798.77215;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:44;a:5:{s:4:"time";d:1756729798.772169;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:45;a:5:{s:4:"time";d:1756729798.772622;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:46;a:5:{s:4:"time";d:1756729798.773839;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:47;a:5:{s:4:"time";d:1756729798.778672;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:48;a:5:{s:4:"time";d:1756729798.778699;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:49;a:5:{s:4:"time";d:1756729798.778715;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:50;a:5:{s:4:"time";d:1756729798.778733;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:51;a:5:{s:4:"time";d:1756729798.778747;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:52;a:5:{s:4:"time";d:1756729798.77876;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:53;a:5:{s:4:"time";d:1756729798.778774;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:54;a:5:{s:4:"time";d:1756729798.778786;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:55;a:5:{s:4:"time";d:1756729798.7788;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:56;a:5:{s:4:"time";d:1756729798.778809;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:57;a:5:{s:4:"time";d:1756729798.778812;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:58;a:5:{s:4:"time";d:1756729798.778814;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:59;a:5:{s:4:"time";d:1756729798.778817;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:60;a:5:{s:4:"time";d:1756729798.778819;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:61;a:5:{s:4:"time";d:1756729798.778822;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:62;a:5:{s:4:"time";d:1756729798.778824;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:63;a:5:{s:4:"time";d:1756729798.778827;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:64;a:5:{s:4:"time";d:1756729798.778829;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:65;a:5:{s:4:"time";d:1756729798.778832;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:66;a:5:{s:4:"time";d:1756729798.778873;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:67;a:5:{s:4:"time";d:1756729798.778876;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:68;a:5:{s:4:"time";d:1756729798.778879;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:69;a:5:{s:4:"time";d:1756729798.778881;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:70;a:5:{s:4:"time";d:1756729798.778884;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:71;a:5:{s:4:"time";d:1756729798.778887;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:72;a:5:{s:4:"time";d:1756729798.778889;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:73;a:5:{s:4:"time";d:1756729798.778892;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:74;a:5:{s:4:"time";d:1756729798.778894;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:75;a:5:{s:4:"time";d:1756729798.778897;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentAntwoord";}i:76;a:5:{s:4:"time";d:1756729798.779601;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:77;a:5:{s:4:"time";d:1756729798.779633;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:78;a:5:{s:4:"time";d:1756729798.779657;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:79;a:5:{s:4:"time";d:1756729798.780381;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:80;a:5:{s:4:"time";d:1756729798.78048;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:81;a:5:{s:4:"time";d:1756729798.780551;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:82;a:5:{s:4:"time";d:1756729798.780571;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:83;a:5:{s:4:"time";d:1756729798.781603;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:84;a:5:{s:4:"time";d:1756729798.782134;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:85;a:5:{s:4:"time";d:1756729798.782756;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:86;a:5:{s:4:"time";d:1756729798.786673;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:87;a:5:{s:4:"time";d:1756729798.786706;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:88;a:5:{s:4:"time";d:1756729798.78673;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:89;a:5:{s:4:"time";d:1756729798.78675;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:90;a:5:{s:4:"time";d:1756729798.78677;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:91;a:5:{s:4:"time";d:1756729798.786793;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:92;a:5:{s:4:"time";d:1756729798.786813;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:93;a:5:{s:4:"time";d:1756729798.786832;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:94;a:5:{s:4:"time";d:1756729798.786851;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:95;a:5:{s:4:"time";d:1756729798.78688;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:96;a:5:{s:4:"time";d:1756729798.787711;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:97;a:5:{s:4:"time";d:1756729798.787754;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:98;a:5:{s:4:"time";d:1756729798.787779;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:99;a:5:{s:4:"time";d:1756729798.787799;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:100;a:5:{s:4:"time";d:1756729798.787817;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:101;a:5:{s:4:"time";d:1756729798.787835;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:102;a:5:{s:4:"time";d:1756729798.787853;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:103;a:5:{s:4:"time";d:1756729798.787874;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:104;a:5:{s:4:"time";d:1756729798.787892;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:105;a:5:{s:4:"time";d:1756729798.78791;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:106;a:5:{s:4:"time";d:1756729798.787921;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:107;a:5:{s:4:"time";d:1756729798.787925;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:108;a:5:{s:4:"time";d:1756729798.787929;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:109;a:5:{s:4:"time";d:1756729798.787932;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:110;a:5:{s:4:"time";d:1756729798.787936;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:111;a:5:{s:4:"time";d:1756729798.787939;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:112;a:5:{s:4:"time";d:1756729798.787943;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:113;a:5:{s:4:"time";d:1756729798.787946;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:114;a:5:{s:4:"time";d:1756729798.78795;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:115;a:5:{s:4:"time";d:1756729798.787953;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"common\models\Vraag";}i:116;a:5:{s:4:"time";d:1756729798.787994;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:117;a:5:{s:4:"time";d:1756729798.787998;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:118;a:5:{s:4:"time";d:1756729798.788001;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:119;a:5:{s:4:"time";d:1756729798.788004;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:120;a:5:{s:4:"time";d:1756729798.788008;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:121;a:5:{s:4:"time";d:1756729798.788011;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:122;a:5:{s:4:"time";d:1756729798.788015;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:123;a:5:{s:4:"time";d:1756729798.788018;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:124;a:5:{s:4:"time";d:1756729798.788022;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:125;a:5:{s:4:"time";d:1756729798.788025;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentTypeVraag";}i:126;a:5:{s:4:"time";d:1756729798.788065;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:127;a:5:{s:4:"time";d:1756729798.788823;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:128;a:5:{s:4:"time";d:1756729798.788931;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:129;a:5:{s:4:"time";d:1756729798.789041;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:130;a:5:{s:4:"time";d:1756729798.790038;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:131;a:5:{s:4:"time";d:1756729798.790072;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:132;a:5:{s:4:"time";d:1756729798.790105;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:133;a:5:{s:4:"time";d:1756729798.790787;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:134;a:5:{s:4:"time";d:1756729798.790821;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:135;a:5:{s:4:"time";d:1756729798.790853;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:136;a:5:{s:4:"time";d:1756729798.791518;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:137;a:5:{s:4:"time";d:1756729798.791552;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:138;a:5:{s:4:"time";d:1756729798.791584;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:139;a:5:{s:4:"time";d:1756729798.792292;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:140;a:5:{s:4:"time";d:1756729798.792384;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:141;a:5:{s:4:"time";d:1756729798.792488;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:142;a:5:{s:4:"time";d:1756729798.793491;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:143;a:5:{s:4:"time";d:1756729798.793525;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:144;a:5:{s:4:"time";d:1756729798.793556;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:145;a:5:{s:4:"time";d:1756729798.79427;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:146;a:5:{s:4:"time";d:1756729798.794305;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:147;a:5:{s:4:"time";d:1756729798.794339;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:148;a:5:{s:4:"time";d:1756729798.794779;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:149;a:5:{s:4:"time";d:1756729798.794797;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:150;a:5:{s:4:"time";d:1756729798.794816;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:151;a:5:{s:4:"time";d:1756729798.795496;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:152;a:5:{s:4:"time";d:1756729798.795715;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:153;a:5:{s:4:"time";d:1756729798.795826;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:154;a:5:{s:4:"time";d:1756729798.797064;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:155;a:5:{s:4:"time";d:1756729798.797107;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:156;a:5:{s:4:"time";d:1756729798.798212;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:157;a:5:{s:4:"time";d:1756729798.804175;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:158;a:5:{s:4:"time";d:1756729798.835758;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:159;a:5:{s:4:"time";d:1756729798.836678;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:160;a:5:{s:4:"time";d:1756729798.836746;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:161;a:5:{s:4:"time";d:1756729798.837728;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:162;a:5:{s:4:"time";d:1756729798.837946;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:163;a:5:{s:4:"time";d:1756729798.838031;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:164;a:5:{s:4:"time";d:1756729798.839472;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:165;a:5:{s:4:"time";d:1756729798.83951;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:166;a:5:{s:4:"time";d:1756729798.839523;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:167;a:5:{s:4:"time";d:1756729798.839526;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\DocumentFeedback";}i:168;a:5:{s:4:"time";d:1756729798.83957;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:169;a:5:{s:4:"time";d:1756729798.840281;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:170;a:5:{s:4:"time";d:1756729798.840314;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:171;a:5:{s:4:"time";d:1756729798.844074;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:172;a:5:{s:4:"time";d:1756729798.845048;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:173;a:5:{s:4:"time";d:1756729798.845074;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:174;a:5:{s:4:"time";d:1756729798.846004;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:175;a:5:{s:4:"time";d:1756729798.852285;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:31:"common\models\DocumentSignature";}i:176;a:5:{s:4:"time";d:1756729798.85248;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:177;a:5:{s:4:"time";d:1756729798.852751;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:178;a:5:{s:4:"time";d:1756729798.852812;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:179;a:5:{s:4:"time";d:1756729798.852879;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\bootstrap5\ActiveForm";}i:180;a:5:{s:4:"time";d:1756729798.8529;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:181;a:5:{s:4:"time";d:1756729798.852906;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:182;a:5:{s:4:"time";d:1756729798.853306;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:183;a:5:{s:4:"time";d:1756729798.853322;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:184;a:5:{s:4:"time";d:1756729798.853369;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:185;a:5:{s:4:"time";d:1756729798.853379;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:186;a:5:{s:4:"time";d:1756729798.853394;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:187;a:5:{s:4:"time";d:1756729798.853445;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:188;a:5:{s:4:"time";d:1756729798.871987;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:189;a:5:{s:4:"time";d:1756729798.872168;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:190;a:5:{s:4:"time";d:1756729798.872562;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:191;a:5:{s:4:"time";d:1756729798.874289;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:192;a:5:{s:4:"time";d:1756729798.874317;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:193;a:5:{s:4:"time";d:1756729798.87537;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:194;a:5:{s:4:"time";d:1756729798.87692;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:195;a:5:{s:4:"time";d:1756729798.877615;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:196;a:5:{s:4:"time";d:1756729798.881942;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:197;a:5:{s:4:"time";d:1756729798.881953;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:198;a:5:{s:4:"time";d:1756729798.881961;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:199;a:5:{s:4:"time";d:1756729798.881963;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:200;a:5:{s:4:"time";d:1756729798.881965;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\NotificationUser";}i:201;a:5:{s:4:"time";d:1756729798.882121;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:202;a:5:{s:4:"time";d:1756729798.882321;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:203;a:5:{s:4:"time";d:1756729798.882327;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:204;a:5:{s:4:"time";d:1756729798.882463;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:205;a:5:{s:4:"time";d:1756729798.882466;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:206;a:5:{s:4:"time";d:1756729798.882514;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:207;a:5:{s:4:"time";d:1756729798.882625;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:208;a:5:{s:4:"time";d:1756729798.882629;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:209;a:5:{s:4:"time";d:1756729798.882641;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:210;a:5:{s:4:"time";d:1756729798.88268;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:211;a:5:{s:4:"time";d:1756729798.882811;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:212;a:5:{s:4:"time";d:1756729798.882815;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:213;a:5:{s:4:"time";d:1756729798.882852;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:214;a:5:{s:4:"time";d:1756729798.883139;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:215;a:5:{s:4:"time";d:1756729798.889142;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:216;a:5:{s:4:"time";d:1756729798.889196;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:217;a:5:{s:4:"time";d:1756729798.890748;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:218;a:5:{s:4:"time";d:1756729798.89085;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:219;a:5:{s:4:"time";d:1756729798.890878;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:220;a:5:{s:4:"time";d:1756729798.890914;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:221;a:5:{s:4:"time";d:1756729798.89132;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:222;a:5:{s:4:"time";d:1756729798.893545;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:223;a:5:{s:4:"time";d:1756729798.893578;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:224;a:5:{s:4:"time";d:1756729798.89377;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:225;a:5:{s:4:"time";d:1756729798.893827;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:226;a:5:{s:4:"time";d:1756729798.894214;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:227;a:5:{s:4:"time";d:1756729798.894908;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:228;a:5:{s:4:"time";d:1756729798.895061;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:229;a:5:{s:4:"time";d:1756729798.895276;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:230;a:5:{s:4:"time";d:1756729798.896446;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:231;a:5:{s:4:"time";d:1756729798.897427;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:232;a:5:{s:4:"time";d:1756729798.898141;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:233;a:5:{s:4:"time";d:1756729798.89821;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"backend\controllers\DocumentController";}i:234;a:5:{s:4:"time";d:1756729798.899245;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:235;a:5:{s:4:"time";d:1756729798.900693;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:236;a:5:{s:4:"time";d:1756729798.90079;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:237;a:5:{s:4:"time";d:1756729798.905057;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:238;a:5:{s:4:"time";d:1756729798.905064;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:239;a:5:{s:4:"time";d:1756729798.90507;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:240;a:5:{s:4:"time";d:1756729798.905344;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:241;a:5:{s:4:"time";d:1756729798.905736;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1756729798.572486;s:3:"end";d:1756729798.911192;s:6:"memory";i:11946704;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:320:"a:3:{s:8:"messages";a:1:{i:36;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1756729798.702447;i:4;a:0:{}i:5;i:7223744;}}s:5:"route";s:17:"document/comments";s:6:"action";s:56:"backend\controllers\DocumentController::actionComments()";}";s:7:"request";s:10897:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:18:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:4:"none";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:957:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=uufjnrrdtap7c0l5hmso25eksa; _csrf-backend=4b2c028abd4cb9ed73cecaa68f4e4b94769254bb1a126e9f9b7b7af4c0b82d71a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22V_kcd61-THFzKMReVQlRoInSnyXaC_bo%22%3B%7D; advanced-frontend-fmz=prfuur9kl8mtful3ksb5da6o24; _csrf-frontend=0073ba50ca3468c28e9c496530b742575fa81b3c8607c46d1c364418eb7caaa3a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22rP5vGQSQw713ow8c1wpk4QWgvtHijHIM%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";s:13:"cache-control";s:9:"max-age=0";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68b591c6a68bc";s:16:"X-Debug-Duration";s:3:"334";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=68b591c6a68bc";s:10:"Set-Cookie";s:311:"_identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; expires=Wed, 01 Oct 2025 12:29:58 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:17:"document/comments";s:6:"action";s:56:"backend\controllers\DocumentController::actionComments()";s:12:"actionParams";a:1:{s:2:"id";s:2:"18";}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:104:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-037703ea-40ef-4cc2-9650-66b41cf8c9ea";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:17:"ChocolateyInstall";s:25:"C:\ProgramData\chocolatey";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:582:"C:\Python313\Scripts\;C:\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\GitHub CLI\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:49:"/backoffice/index.php?r=document%2Fcomments&id=18";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"60972";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:27:"r=document%2Fcomments&id=18";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:4:"none";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:957:"_identity-frontend=ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; advanced-backend-fmz=uufjnrrdtap7c0l5hmso25eksa; _csrf-backend=4b2c028abd4cb9ed73cecaa68f4e4b94769254bb1a126e9f9b7b7af4c0b82d71a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22V_kcd61-THFzKMReVQlRoInSnyXaC_bo%22%3B%7D; advanced-frontend-fmz=prfuur9kl8mtful3ksb5da6o24; _csrf-frontend=0073ba50ca3468c28e9c496530b742575fa81b3c8607c46d1c364418eb7caaa3a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22rP5vGQSQw713ow8c1wpk4QWgvtHijHIM%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:1756729798.556135;s:12:"REQUEST_TIME";i:1756729798;}s:3:"GET";a:2:{s:1:"r";s:17:"document/comments";s:2:"id";s:2:"18";}s:4:"POST";a:0:{}s:6:"COOKIE";a:7:{s:18:"_identity-frontend";s:158:"ea504c4adbb00d93bcc48cc313183654e5c3f219dbab61d7d91fbc33aaa0c4a5a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"_identity-backend";s:157:"39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";s:20:"advanced-backend-fmz";s:26:"uufjnrrdtap7c0l5hmso25eksa";s:13:"_csrf-backend";s:139:"4b2c028abd4cb9ed73cecaa68f4e4b94769254bb1a126e9f9b7b7af4c0b82d71a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"V_kcd61-THFzKMReVQlRoInSnyXaC_bo";}";s:21:"advanced-frontend-fmz";s:26:"prfuur9kl8mtful3ksb5da6o24";s:14:"_csrf-frontend";s:140:"0073ba50ca3468c28e9c496530b742575fa81b3c8607c46d1c364418eb7caaa3a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"rP5vGQSQw713ow8c1wpk4QWgvtHijHIM";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:7895:"a:17:{s:30:"yii\validators\ValidationAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:17:"yii.validation.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\Reclassering/vendor/bower-asset/jquery/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\8bc86ca8";s:7:"baseUrl";s:27:"/backoffice/assets/8bc86ca8";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:27:"yii\widgets\ActiveFormAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:17:"yii.activeForm.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap5\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap5\BootstrapAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\4ceb4c69";s:7:"baseUrl";s:27:"/backoffice/assets/4ceb4c69";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:41:"hail812\adminlte3\assets\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:74:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins/fontawesome-free";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\9a5ee6fe";s:7:"baseUrl";s:27:"/backoffice/assets/9a5ee6fe";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:34:"hail812\adminlte3\assets\BaseAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";N;s:7:"baseUrl";N;s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";i:2;s:35:"yii\bootstrap4\BootstrapPluginAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:36:"hail812\adminlte3\assets\PluginAsset";a:9:{s:10:"sourcePath";s:57:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\834b4d89";s:7:"baseUrl";s:27:"/backoffice/assets/834b4d89";s:7:"depends";a:1:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:46:"/backoffice/assets/e4a6bb80/control_sidebar.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:39:"\hail812\adminlte3\assets\AdminLteAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:45:"backoffice/assets/e4a6bb80/control_sidebar.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:39:"\hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\f545690b";s:7:"baseUrl";s:27:"/backoffice/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:23:"backend\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:31:"C:\Web\Reclassering\backend\web";s:7:"baseUrl";s:11:"/backoffice";s:7:"depends";a:7:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:19:"yii\web\JqueryAsset";i:3;s:16:"yii\jui\JuiAsset";i:4;s:29:"kartik\sortable\SortableAsset";i:5;s:38:"hail812\adminlte3\assets\AdminLteAsset";i:6;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:3:{i:0;s:50:"https://unpkg.com/lucide@latest/dist/umd/lucide.js";i:1;s:10:"js/main.js";i:2;s:75:"https://cdn.jsdelivr.net/npm/sweetalert2@11.7.1/dist/sweetalert2.all.min.js";}s:3:"css";a:1:{i:0;s:12:"css/site.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\jui\JuiAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\Reclassering/vendor/bower-asset/jquery-ui";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\a78fe255";s:7:"baseUrl";s:27:"/backoffice/assets/a78fe255";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:12:"jquery-ui.js";}s:3:"css";a:1:{i:0;s:31:"themes/smoothness/jquery-ui.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"kartik\sortable\SortableAsset";a:17:{s:10:"sourcePath";s:60:"C:\Web\Reclassering\vendor\kartik-v\yii2-sortable\src/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\3202e5a2";s:7:"baseUrl";s:27:"/backoffice/assets/3202e5a2";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:2:{i:0;s:19:"js/html5sortable.js";i:1;s:23:"js/kv-html5-sortable.js";}s:3:"css";a:1:{i:0;s:25:"css/kv-html5-sortable.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68b591c6a68bc";s:3:"url";s:70:"http://localhost:8005/backoffice/index.php?r=document%2Fcomments&id=18";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:1756729798.556135;s:10:"statusCode";i:200;s:8:"sqlCount";i:67;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11946704;s:14:"processingTime";d:0.336806058883667;}s:10:"exceptions";a:0:{}}