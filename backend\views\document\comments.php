<?php

use common\components\SignatureHelper;
use yii\helpers\Html;
use yii\bootstrap5\ActiveForm;
use yii\helpers\Url;
use yii\bootstrap5\Modal;

/* @var $this yii\web\View */
/* @var $model common\models\Document */
/* @var $documentData array */

$this->title = 'Feedback';
// $this->params['breadcrumbs'][] = ['label' => 'Documenten', 'url' => ['/document']];
// $this->params['breadcrumbs'][] = $this->title;

// Get the PDF URL
$pdfUrl = Url::to(['document/pdf', 'id' => $model->id]);
$markFinalUrl = \yii\helpers\Url::to(['document/mark-document-as-final', 'id' => $model->id]);


// Get current URL for redirect
$currentUrl = Url::current();

$updateUrl = Url::to(['/document-feedback/update']); // Add leading slash to ensure absolute path
$nextStatus = Url::to(['/document/go-to-next-status']); // Add leading slash to ensure absolute path
?>

<div class="document-view row">
    <!-- Left side - PDF Preview -->
    <div class="col-md-6">
        <iframe
            src="<?= $pdfUrl ?>"
            width="100%"
            height="800px"
            style="border: none; border-radius: 6px;"
            title="Document PDF Preview">
        </iframe>
    </div>

    <!-- Right side - Feedback Form -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h5 class="mb-4">
                    Document status:
                    <span class="badge bg-danger">
                        <?= $model->workflowStatus->label ?>
                    </span>
                </h5>
                <?php $form = ActiveForm::begin([
                    'id' => 'feedback-form',
                    'action' => ['/document-feedback/create'],
                    'enableAjaxValidation' => false,
                ]); ?>

                <?= Html::activeHiddenInput($feedbackModel, 'document_id', ['value' => $model->id]) ?>
                <?= Html::hiddenInput('redirect_url', $currentUrl, ['id' => 'redirect-url']) ?>

                <?= $form->field($feedbackModel, 'comment')->textarea([
                    'rows' => 6,
                    'placeholder' => 'Enter your feedback here...',
                ]) ?>

                <?php if (!SignatureHelper::hasSignatureFromRoles($documentData['signatures'])): ?>
                    <div class="form-group">
                        <?= Html::submitButton('Save', [
                            'class' => 'btn btn-outline-dark',
                            'id' => 'submit-feedback',
                        ]) ?>
                        <?php if (isHoofdOrOnderhoofd()) {
                            echo Html::button('Bevestig', [
                                'class' => 'btn btn-outline-danger',
                                'id' => 'bevestig-btn',
                                'disabled' => $documentData['status'] === 'hoofd',
                            ]);

                            // Show the Signature btn if status is hoofd and there is no signature from superuser or hoofd
                            if ($documentData['status'] === 'hoofd' && !SignatureHelper::hasSignatureFromRoles($documentData['signatures'], [1, 3])) {
                                echo Html::button('<i data-lucide="signature" style="width:18px; height:18px"></i> Ondertekenen', [
                                    'class' => 'btn btn-outline-warning ml-1',
                                    'id' => 'signature-btn',
                                    'disabled' => SignatureHelper::hasSignatureFromRoles($documentData['signatures'], [1, 3])
                                ]);
                            }

                            // Show signature button if document is marked as final
                            if ($model->mark_final == 1) {
                                echo Html::button('<i data-lucide="signature" style="width:18px; height:18px"></i> Ondertekenen', [
                                    'class' => 'btn btn-outline-warning ml-1',
                                    'id' => 'signature-btn',
                                ]);
                            }
                        } ?>
                    </div>

                <?php endif; ?>
                <?php ActiveForm::end(); ?>

                <!-- Mark document as Final Checkbox -->
                <?php if ($documentData['status'] === 'onderhoofd'): ?>
                    <?php $form = ActiveForm::begin(['id' => 'final-form']); ?>
                    <?= $form->field($model, 'mark_final')->checkbox([
                        'id' => 'mark-final-checkbox',
                        'label' => 'Markeer document als finale versie',
                        'uncheck' => 0,   // value saved when unchecked
                        'value' => 1,     // value saved when checked
                    ]) ?>
                    <?php ActiveForm::end(); ?>
                <?php endif; ?>

                <!-- Display Existing Feedback -->
                <?php if (!empty($model->documentFeedbacks)): ?>
                    <h5 class="mt-4">Previous Feedback</h5>
                    <div class="previous-feedback" style="max-height: 400px; overflow-y: auto; padding-right: 10px;">
                        <?php
                        $sortedFeedbacks = $model->documentFeedbacks;
                        usort($sortedFeedbacks, function ($a, $b) {
                            return strtotime($b->created_at) - strtotime($a->created_at);
                        });
                        foreach ($sortedFeedbacks as $feedback): ?>
                            <div class="card mb-3" data-feedback-id="<?= $feedback->id ?>">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <h6 class="card-subtitle mb-2 text-muted">
                                            By <?= Html::encode($feedback->createdBy->username) ?>
                                            on <?= Yii::$app->formatter->asDatetime($feedback->created_at) ?>
                                        </h6>
                                        <?php if ($feedback->created_by === Yii::$app->user->id && $feedback === reset($sortedFeedbacks)): ?>
                                            <button class="btn btn-link p-0 edit-feedback"
                                                title="Edit feedback"
                                                data-feedback-id="<?= $feedback->id ?>">
                                                <i data-lucide="pencil" style="width:18px; height:18px"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                    <div class="feedback-content">
                                        <p class="card-text mb-0"><?= Html::encode($feedback->comment) ?></p>
                                    </div>
                                    <div class="feedback-edit-form d-none">
                                        <textarea class="form-control mb-2"><?= Html::encode($feedback->comment) ?></textarea>
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-primary btn-sm save-edit">Save</button>
                                            <button class="btn btn-outline-secondary btn-sm cancel-edit">Cancel</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
$script = <<<JS
    // Get the document_id from the hidden input field
    const documentId = $('input[name="DocumentFeedback[document_id]"]').val();

    $('#feedback-form').on('beforeSubmit', function(e) {
    e.preventDefault();
    
    $.ajax({
        url: $(this).attr('action'),
        type: 'POST',
        data: $(this).serialize() + '&redirect_url=' + $('#redirect-url').val(),
        success: function(response) {
            if (response.success) {
                Toast.fire({
                    icon: 'success',
                    title: response.message || 'Feedback saved successfully'
                });
                
                // Redirect after a short delay to allow the toast to be visible
                setTimeout(function() {
                    window.location.href = $('#redirect-url').val();
                }, 1500);
            } else {
                Toast.fire({
                    icon: 'error',
                    title: response.message || 'Error saving feedback'
                });
            }
        },
        error: function(xhr, status, error) {
            Toast.fire({
                icon: 'error',
                title: 'Error submitting feedback: ' + error
            });
        }
    });
    
    return false;
});


// Handle edit feedback functionality
$('.edit-feedback').on('click', function() {
    const feedbackId = $(this).data('feedback-id');
    
    const card = $(this).closest('.card');
    card.find('.feedback-content').addClass('d-none');
    card.find('.feedback-edit-form').removeClass('d-none');
    
    // Store the ID on the save button for later use
    card.find('.save-edit').data('feedback-id', feedbackId);
});

$('.cancel-edit').on('click', function() {
    const card = $(this).closest('.card');
    card.find('.feedback-edit-form').addClass('d-none');
    card.find('.feedback-content').removeClass('d-none');
});

$('.save-edit').on('click', function() {
    const feedbackId = $(this).data('feedback-id');    
    const card = $(this).closest('.card');
    const newComment = card.find('textarea').val();
    const redirectUrl = $('#redirect-url').val();


    if (!feedbackId) {
        Toast.fire({
            icon: 'error',
            title: 'Error: Could not determine feedback ID'
        });
        return;
    }
    
    $.ajax({
        url: updateUrl + '&id=' + feedbackId,
        type: 'POST',
        data: {
            '_csrf-backend': yii.getCsrfToken(),
            'DocumentFeedback[document_id]': documentId,
            'DocumentFeedback[comment]': newComment,
            'redirect_url': redirectUrl
        },
        success: function(response) {
            if (response.success) {
                Toast.fire({
                    icon: 'success',
                    title: response.message || 'Feedback updated successfully'
                });
                
                // Update the displayed comment
                card.find('.feedback-content p.card-text').text(newComment);
                card.find('.feedback-edit-form').addClass('d-none');
                card.find('.feedback-content').removeClass('d-none');
            } else {
                Toast.fire({
                    icon: 'error',
                    title: response.message || 'Error updating feedback'
                });
            }
        },
        error: function(xhr, status, error) {
            Toast.fire({
                icon: 'error',
                title: 'Error updating feedback: ' + xhr.responseText
            });
        }
    });
});

// Add custom scrollbar styling
document.querySelector('.previous-feedback').style.scrollbarWidth = 'thin';
document.querySelector('.previous-feedback').style.scrollbarColor = '#6c757d #f8f9fa';

$('#bevestig-btn').on('click', function() {
$.ajax({
        url: nextStatus,
        type: 'POST',
        data: {
            id: documentId,
            _csrf: yii.getCsrfToken()
        },
        success: function(response) {
            if (response.success) {
                Toast.fire({
                    icon: 'success',
                    title: response.message || 'Status updated successfully'
                });
                
                // Reload the page after a short delay
                setTimeout(function() {
                    window.location.reload();
                }, 1500);
            } else {
                Toast.fire({
                    icon: 'error',
                    title: response.message || 'Failed to update status'
                });
            }
        },
        error: function(xhr, status, error) {
            Toast.fire({
                icon: 'error',
                title: 'Error: ' + (xhr.responseText || error)
            });
        }
    });
});

$('#signature-btn').on('click', function() {
    $('#signature-modal').modal('show');
})

//----------- Show confirmation modal when marking document as final ---------------
$(document).on('change', '#mark-final-checkbox', function () {
    if ($(this).is(':checked')) {
        $('#confirm-final-modal').modal('show');
    }
});

$(document).on('click', '#confirm-final-btn', function () {
    $.ajax({
        url: markFinalUrl,
        type: 'POST',
        data: { _csrf: yii.getCsrfToken()},
        success: function (response) {
            if (response.success) {
                 Toast.fire({
                    icon: 'success',
                    title: response.message
                });

                settim
                window.location.reload();
            } else {
                Toast.fire({
                    icon: 'error',
                    title: response.message || 'Probeer later opnieuw.'
                });
                $('#mark-final-checkbox').prop('checked', false);
            }
        },
        error: function () {
            alert('Er is een fout opgetreden.');
            $('#mark-final-checkbox').prop('checked', false);
        }
    });

    $('#confirm-final-modal').modal('hide');
});

$('#confirm-final-modal').on('hidden.bs.modal', function () {
    if (!$('#confirm-final-btn').data('confirmed')) {
        $('#mark-final-checkbox').prop('checked', false);
    }
});
JS;
$this->registerJs($script);

$this->registerJsVar('updateUrl', $updateUrl); // Pass $updateUrl as a JavaScript variable
$this->registerJsVar('nextStatus', $nextStatus); // Pass $updateUrl as a JavaScript variable
$this->registerJsVar('markFinalUrl', $markFinalUrl); // Pass $updateUrl as a JavaScript variable

// Add custom CSS
$css = <<<CSS
.previous-feedback::-webkit-scrollbar {
    width: 6px;
}

.previous-feedback::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 3px;
}

.previous-feedback::-webkit-scrollbar-thumb {
    background: #6c757d;
    border-radius: 3px;
}

.previous-feedback::-webkit-scrollbar-thumb:hover {
    background: #495057;
}
CSS;
$this->registerCss($css);

Modal::begin([
    'id' => 'signature-modal',
    // 'title' => '<h5 class="modal-title">Ondertekenen</h5>',
    'size' => Modal::SIZE_DEFAULT,
    'centerVertical' => true,
    'closeButton' => [
        'class' => 'btn-close',
        'data-bs-dismiss' => 'modal',
        'aria-label' => 'Close'
    ],
]);
echo $this->render('/document-signature/_signature-form', [
    'model' => new \common\models\DocumentSignature(['document_id' => $model->id]),
]);
Modal::end();


Modal::begin([
    'id' => 'confirm-final-modal',
    'title' => 'Bevestiging',
    'footer' => '
        <button type="button" class="btn btn-outline-secondary rounded-2" data-bs-dismiss="modal">Nee</button>
        <button type="button" id="confirm-final-btn" class="rounded-2 btn btn-success">Ja, markeer als final</button>
    ',
]);
echo "<p>Weet u zeker dat u dit document als finale versie wilt markeren?</p>";
Modal::end();
?>